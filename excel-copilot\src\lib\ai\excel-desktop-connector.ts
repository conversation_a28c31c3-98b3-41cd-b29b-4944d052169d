/**
 * Integração entre o processador de IA e o Excel Desktop conectado
 *
 * Este módulo facilita a comunicação entre o processador de comandos IA
 * e o Excel Desktop, permitindo a execução de operações diretamente no Excel instalado.
 */

import { ExcelOperationType, ExcelOperation } from '@/types';

import { logger } from '../logger';

/**
 * Resultado de uma operação Excel
 */
export interface ExcelOperationResult {
  success: boolean;
  message: string;
  data?: any;
  error: string | undefined;
}

/**
 * Classe que conecta o processador de IA ao Excel Desktop
 */
export class ExcelDesktopConnector {
  private static instance: ExcelDesktopConnector;

  private constructor() {}

  /**
   * Obtém a instância singleton
   */
  public static getInstance(): ExcelDesktopConnector {
    if (!ExcelDesktopConnector.instance) {
      ExcelDesktopConnector.instance = new ExcelDesktopConnector();
    }
    return ExcelDesktopConnector.instance;
  }

  /**
   * Verifica se o Excel Desktop está conectado
   */
  public isExcelConnected(): boolean {
    // Desktop bridge não disponível - sempre retorna false
    return false;
  }

  /**
   * Executa uma operação no Excel Desktop
   * @param operation Operação a ser executada
   */
  public async executeOperation(operation: ExcelOperation): Promise<ExcelOperationResult> {
    // Desktop bridge não disponível
    logger.info(`Operação simulada (desktop bridge não disponível): ${operation.type}`, operation);

    return {
      success: false,
      message: 'Excel Desktop não está disponível nesta versão',
      error: 'DESKTOP_BRIDGE_NOT_AVAILABLE',
    };
  }

  /**
   * Executa múltiplas operações em sequência
   * @param operations Lista de operações a serem executadas
   */
  public async executeOperations(operations: ExcelOperation[]): Promise<ExcelOperationResult[]> {
    const results: ExcelOperationResult[] = [];

    for (const operation of operations) {
      const result = await this.executeOperation(operation);
      results.push(result);

      // Se alguma operação falhar, interromper a sequência
      if (!result.success) {
        break;
      }
    }

    return results;
  }

  /**
   * Abre um arquivo Excel
   */
  public async openExcelFile(): Promise<ExcelOperationResult> {
    // Desktop bridge não disponível
    return {
      success: false,
      message: 'Abertura de arquivo Excel não disponível nesta versão',
      error: 'DESKTOP_BRIDGE_NOT_AVAILABLE',
    };
  }

  /**
   * Obtém informações sobre o arquivo Excel atual
   */
  public async getWorkbookInfo(): Promise<ExcelOperationResult> {
    return await this.executeOperation({
      type: 'GET_WORKBOOK_INFO' as ExcelOperationType,
    });
  }

  /**
   * Obtém dados da planilha ativa
   */
  public async getActiveWorksheetData(): Promise<ExcelOperationResult> {
    return await this.executeOperation({
      type: 'GET_WORKSHEET_DATA' as ExcelOperationType,
    });
  }
}
