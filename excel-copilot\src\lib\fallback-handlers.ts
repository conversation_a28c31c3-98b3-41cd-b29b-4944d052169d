/**
 * 🛡️ FALLBACK HANDLERS
 *
 * Implementa fallbacks para quando serviços críticos falham
 */

import { logger } from './logger';

/**
 * Implementação simples de circuit breaker para substituir o módulo não disponível
 */
interface SimpleCircuitBreaker {
  execute<T>(operation: () => Promise<T>): Promise<T>;
}

const createSimpleCircuitBreaker = (): SimpleCircuitBreaker => ({
  execute: async <T>(operation: () => Promise<T>): Promise<T> => {
    return await operation();
  },
});

// Substituto para createCircuitBreaker
const createCircuitBreaker = {
  vertexAI: () => createSimpleCircuitBreaker(),
  stripe: () => createSimpleCircuitBreaker(),
  mcp: (_integration: string) => createSimpleCircuitBreaker(),
  redis: () => createSimpleCircuitBreaker(),
};

/**
 * Resultado de operação com fallback
 */
export interface FallbackResult<T> {
  data: T;
  source: 'primary' | 'fallback' | 'cache' | 'mock';
  warning?: string;
}

/**
 * Configuração de fallback
 */
export interface FallbackConfig {
  enableCache: boolean;
  cacheTimeout: number;
  enableMocks: boolean;
  retryAttempts: number;
  retryDelay: number;
}

/**
 * Cache simples em memória para fallbacks
 */
class FallbackCache {
  private cache = new Map<string, { data: unknown; timestamp: number; ttl: number }>();

  set(key: string, data: unknown, ttl: number = 300000): void {
    // 5 min default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get(key: string): unknown | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  clear(): void {
    this.cache.clear();
  }
}

const fallbackCache = new FallbackCache();

/**
 * Fallback para Vertex AI
 */
export class VertexAIFallback {
  private static readonly MOCK_RESPONSES = {
    'analyze-data':
      'Os dados foram analisados com sucesso. Encontrei padrões interessantes que podem ajudar na tomada de decisões.',
    'generate-formula': '=SOMA(A1:A10)',
    'explain-data':
      'Esta planilha contém dados financeiros organizados por período. Recomendo criar gráficos para melhor visualização.',
    default:
      'Desculpe, o serviço de IA está temporariamente indisponível. Tente novamente em alguns minutos.',
  };

  static async executeWithFallback<T>(
    operation: () => Promise<T>,
    operationType: string = 'default',
    config: Partial<FallbackConfig> = {}
  ): Promise<FallbackResult<T>> {
    const finalConfig: FallbackConfig = {
      enableCache: true,
      cacheTimeout: 300000, // 5 minutos
      enableMocks: process.env.AI_USE_MOCK === 'true',
      retryAttempts: 2,
      retryDelay: 1000,
      ...config,
    };

    const cacheKey = `vertex-ai-${operationType}-${Date.now()}`;
    const circuitBreaker = createCircuitBreaker.vertexAI();

    // Tenta cache primeiro
    if (finalConfig.enableCache) {
      const cached = fallbackCache.get(cacheKey);
      if (cached) {
        logger.info('VertexAI: Using cached response', { operationType });
        return {
          data: cached as T,
          source: 'cache',
          warning: 'Resposta obtida do cache devido a indisponibilidade do serviço',
        };
      }
    }

    // Tenta operação principal com circuit breaker
    try {
      const result = await circuitBreaker.execute(operation);

      // Salva no cache em caso de sucesso
      if (finalConfig.enableCache) {
        fallbackCache.set(cacheKey, result, finalConfig.cacheTimeout);
      }

      return {
        data: result,
        source: 'primary',
      };
    } catch (error) {
      logger.warn('VertexAI: Primary operation failed, using fallback', {
        error: error instanceof Error ? error.message : 'Unknown error',
        operationType,
      });

      // Fallback para mock se habilitado
      if (finalConfig.enableMocks) {
        const mockResponse =
          this.MOCK_RESPONSES[operationType as keyof typeof this.MOCK_RESPONSES] ||
          this.MOCK_RESPONSES.default;

        return {
          data: mockResponse as T,
          source: 'mock',
          warning: 'Serviço de IA temporariamente indisponível. Resposta simulada fornecida.',
        };
      }

      // Se não há fallback disponível, propaga o erro
      throw error;
    }
  }
}

/**
 * Fallback para Stripe
 */
export class StripeFallback {
  static async executeWithFallback<T>(
    operation: () => Promise<T>,
    operationType: string,
    config: Partial<FallbackConfig> = {}
  ): Promise<FallbackResult<T>> {
    const finalConfig: FallbackConfig = {
      enableCache: false, // Não cachear operações financeiras
      cacheTimeout: 0,
      enableMocks: process.env.NODE_ENV === 'development',
      retryAttempts: 3,
      retryDelay: 2000,
      ...config,
    };

    const circuitBreaker = createCircuitBreaker.stripe();

    try {
      const result = await circuitBreaker.execute(operation);
      return {
        data: result,
        source: 'primary',
      };
    } catch (error) {
      logger.error('Stripe: Operation failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        operationType,
      });

      // Para operações críticas de pagamento, não usar fallback
      if (operationType.includes('payment') || operationType.includes('charge')) {
        throw new Error(
          'Serviço de pagamentos temporariamente indisponível. Tente novamente em alguns minutos.'
        );
      }

      // Para consultas, pode usar mock em desenvolvimento
      if (finalConfig.enableMocks && operationType.includes('retrieve')) {
        const mockData = this.getMockData(operationType);
        return {
          data: mockData as T,
          source: 'mock',
          warning: 'Dados simulados - ambiente de desenvolvimento',
        };
      }

      throw error;
    }
  }

  private static getMockData(operationType: string): Record<string, unknown> {
    switch (operationType) {
      case 'retrieve-customer':
        return {
          id: 'cus_mock123',
          email: '<EMAIL>',
          created: Date.now(),
          subscriptions: { data: [] },
        };
      case 'retrieve-subscription':
        return {
          id: 'sub_mock123',
          status: 'active',
          current_period_end: Date.now() + 30 * 24 * 60 * 60 * 1000,
        };
      default:
        return { id: 'mock_data', status: 'active' };
    }
  }
}

/**
 * Fallback para integrações MCP
 */
export class MCPFallback {
  static async executeWithFallback<T>(
    operation: () => Promise<T>,
    integration: string,
    operationType: string,
    config: Partial<FallbackConfig> = {}
  ): Promise<FallbackResult<T>> {
    const finalConfig: FallbackConfig = {
      enableCache: true,
      cacheTimeout: 180000, // 3 minutos
      enableMocks: true,
      retryAttempts: 2,
      retryDelay: 1000,
      ...config,
    };

    const cacheKey = `mcp-${integration}-${operationType}`;
    const circuitBreaker = createCircuitBreaker.mcp(integration);

    // Verifica cache
    if (finalConfig.enableCache) {
      const cached = fallbackCache.get(cacheKey);
      if (cached) {
        return {
          data: cached as T,
          source: 'cache',
          warning: `Dados do ${integration} obtidos do cache`,
        };
      }
    }

    try {
      const result = await circuitBreaker.execute(operation);

      if (finalConfig.enableCache) {
        fallbackCache.set(cacheKey, result, finalConfig.cacheTimeout);
      }

      return {
        data: result,
        source: 'primary',
      };
    } catch (error) {
      logger.warn(`MCP ${integration}: Operation failed, using fallback`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        operationType,
      });

      if (finalConfig.enableMocks) {
        const mockData = this.getMockMCPData(integration, operationType);
        return {
          data: mockData as T,
          source: 'mock',
          warning: `Integração ${integration} temporariamente indisponível`,
        };
      }

      throw error;
    }
  }

  private static getMockMCPData(integration: string, operationType: string): unknown {
    const mockData: Record<string, Record<string, unknown>> = {
      vercel: {
        deployments: [{ id: 'mock_deployment', status: 'ready', url: 'https://mock.vercel.app' }],
        projects: [{ id: 'mock_project', name: 'Mock Project' }],
      },
      linear: {
        issues: [{ id: 'MOCK-1', title: 'Mock Issue', status: 'Todo' }],
        teams: [{ id: 'mock_team', name: 'Mock Team' }],
      },
      github: {
        repositories: [{ id: 'mock_repo', name: 'mock-repository', private: false }],
        commits: [{ sha: 'mock_sha', message: 'Mock commit' }],
      },
    };

    return mockData[integration]?.[operationType] || { message: 'Mock data not available' };
  }
}

/**
 * Fallback para Redis
 */
export class RedisFallback {
  private static memoryCache = new Map<string, { data: unknown; timestamp: number }>();

  static async executeWithFallback<T>(
    operation: () => Promise<T>,
    key: string,
    config: Partial<FallbackConfig> = {}
  ): Promise<FallbackResult<T>> {
    const finalConfig: FallbackConfig = {
      enableCache: true,
      cacheTimeout: 300000, // 5 minutos
      enableMocks: true,
      retryAttempts: 2,
      retryDelay: 500,
      ...config,
    };

    const circuitBreaker = createCircuitBreaker.redis();

    try {
      const result = await circuitBreaker.execute(operation);

      // Backup em memória
      if (finalConfig.enableCache) {
        this.memoryCache.set(key, {
          data: result,
          timestamp: Date.now(),
        });
      }

      return {
        data: result,
        source: 'primary',
      };
    } catch (error) {
      logger.warn('Redis: Operation failed, using memory fallback', {
        error: error instanceof Error ? error.message : 'Unknown error',
        key,
      });

      // Fallback para cache em memória
      const cached = this.memoryCache.get(key);
      if (cached && Date.now() - cached.timestamp < finalConfig.cacheTimeout) {
        return {
          data: cached.data as T,
          source: 'cache',
          warning: 'Redis indisponível, usando cache em memória',
        };
      }

      // Se não há cache, retorna null ou valor padrão
      return {
        data: null as T,
        source: 'fallback',
        warning: 'Cache indisponível, dados podem estar desatualizados',
      };
    }
  }
}

/**
 * Utilitário para limpar todos os caches de fallback
 */
export function clearAllFallbackCaches(): void {
  fallbackCache.clear();
  RedisFallback['memoryCache'].clear();
  logger.info('All fallback caches cleared');
}
