'use client';

import React, { useEffect, useCallback } from 'react';
import { MessageSquare } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { useAIChat } from '@/hooks/useAIChat';

// Componentes refatorados
import { SpreadsheetProvider, SpreadsheetData } from './SpreadsheetContext';
import { useSpreadsheetData } from './hooks/useSpreadsheetData';
import { useSpreadsheetUI } from './hooks/useSpreadsheetUI';
import { useSpreadsheetKeyboard } from './hooks/useSpreadsheetKeyboard';
import { SpreadsheetToolbar } from './components/SpreadsheetToolbar';
import { SpreadsheetGrid } from './components/SpreadsheetGrid';
import { AIAssistantPanel } from './components/AIAssistantPanel';
import { MobileChat } from './components/MobileChat';
import { SpreadsheetModals } from './components/SpreadsheetModals';

interface SpreadsheetEditorProps {
  workbookId: string;
  initialData?: SpreadsheetData;
  readOnly?: boolean;
  onSave?: (data: SpreadsheetData) => Promise<void>;
  initialCommand?: string | null;
}

/**
 * Componente interno que usa os hooks do contexto
 */
function SpreadsheetEditorInner({
  workbookId,
  onSave,
  initialCommand,
}: Omit<SpreadsheetEditorProps, 'initialData'>) {
  // Hooks de dados e UI
  const {
    data,
    isSaving,
    readOnly,
    saveSpreadsheet,
    handleCellChange,
    handleOperations,
    undo,
    redo,
    addColumn,
    addRow,
    removeRow,
    removeColumn,
    canUndo,
    canRedo,
    lastModifiedCell,
    isExcelProcessing,
    _isRealtimeConnected,
  } = useSpreadsheetData({ workbookId, onSave, initialCommand });

  const {
    ui,
    apiUsageInfo,
    toggleAIPanel,
    toggleCommandPalette,
    toggleMobileChat,
    toggleFullScreen,
    showAIIndicator,
    handleChatInputChange,
    handleNextTutorialStep,
    handleCloseTutorial,
    handleUpgradeProClick,
    handleTrialClick,
    handleNavigationClick,
    handleEscapeKey,
    isMobile,
    shouldShowUpgradeAlert,
  } = useSpreadsheetUI();

  const { showKeyboardShortcuts, hideKeyboardShortcuts, keyboardShortcuts } = useSpreadsheetKeyboard({
    onSave: saveSpreadsheet,
    onUndo: undo,
    onRedo: redo,
    onToggleCommandPalette: toggleCommandPalette,
    onToggleFullScreen: toggleFullScreen,
    onEscape: handleEscapeKey,
  });

  // Hook de IA para chat
  const {
    sendMessage,
    isProcessing: isAIProcessing,
    _messages,
    error: _aiError,
  } = useAIChat({
    workbookId,
    onMessageReceived: (content: string) => {
      if (content) {
        try {
          const jsonContent = JSON.parse(content);
          if (jsonContent.operations) {
            handleOperations(jsonContent.operations);
          }
        } catch (err) {
          console.error('Erro ao processar resposta da IA:', err);
        }
      }
    },
  });

  // Desktop Bridge removido - funcionalidade não disponível

  // Handler para processar comandos de IA
  const handleProcessCommand = useCallback(
    (command: string) => {
      if (!command.trim()) return;

      showAIIndicator();
      handleChatInputChange('');
      sendMessage(command, data);
    },
    [sendMessage, data, showAIIndicator, handleChatInputChange]
  );

  // Efeito para mostrar alertas de upgrade
  useEffect(() => {
    if (shouldShowUpgradeAlert) {
      // Lógica para mostrar alerta de upgrade pode ser implementada aqui
    }
  }, [shouldShowUpgradeAlert]);

  return (
    <div className={`h-screen flex flex-col ${ui.isFullScreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Toolbar */}
      <SpreadsheetToolbar
        isSaving={isSaving}
        canUndo={canUndo}
        canRedo={canRedo}
        isFullScreen={ui.isFullScreen}
        aiPanelCollapsed={ui.aiPanelCollapsed}
        readOnly={readOnly}
        spreadsheetName={data.name}
        onSave={saveSpreadsheet}
        onUndo={undo}
        onRedo={redo}
        onToggleFullScreen={toggleFullScreen}
        onToggleAIPanel={toggleAIPanel}
        onShowKeyboardShortcuts={showKeyboardShortcuts}
        onNavigate={handleNavigationClick}
      />

      {/* Conteúdo principal */}
      <div className="flex flex-1 overflow-hidden">
        {/* Grid da planilha */}
        <SpreadsheetGrid
          data={data}
          lastModifiedCell={lastModifiedCell}
          readOnly={readOnly}
          onCellChange={handleCellChange}
          onAddColumn={addColumn}
          onAddRow={addRow}
          onRemoveRow={removeRow}
          onRemoveColumn={removeColumn}
        />

        {/* Painel de IA (Desktop) */}
        {!isMobile && (
          <AIAssistantPanel
            collapsed={ui.aiPanelCollapsed}
            showAiIndicator={ui.showAiIndicator}
            showSuggestions={ui.showSuggestions}
            inputText={ui.inputText}
            isProcessing={isAIProcessing || isExcelProcessing}
            readOnly={readOnly}
            spreadsheetData={data}
            apiUsageInfo={apiUsageInfo}
            onInputChange={handleChatInputChange}
            onProcessCommand={handleProcessCommand}
            onUpgradeClick={handleUpgradeProClick}
            onTrialClick={handleTrialClick}
          />
        )}
      </div>

      {/* Botão flutuante para chat mobile */}
      {isMobile && !ui.showMobileChat && (
        <Button
          className="fixed bottom-4 right-4 h-14 w-14 rounded-full shadow-lg z-40"
          onClick={toggleMobileChat}
          disabled={readOnly}
        >
          <MessageSquare className="h-6 w-6" />
        </Button>
      )}

      {/* Chat Mobile */}
      <MobileChat
        isOpen={ui.showMobileChat}
        inputText={ui.inputText}
        isProcessing={isAIProcessing || isExcelProcessing}
        readOnly={readOnly}
        spreadsheetData={data}
        onClose={toggleMobileChat}
        onInputChange={handleChatInputChange}
        onProcessCommand={handleProcessCommand}
      />

      {/* Modais */}
      <SpreadsheetModals
        showKeyboardShortcuts={ui.showKeyboardShortcuts}
        showUpgradeModal={ui.showUpgradeModal}
        showFeedback={ui.showFeedback}
        showTutorial={ui.showTutorial}
        tutorialStep={ui.tutorialStep}
        keyboardShortcuts={keyboardShortcuts}
        onCloseKeyboardShortcuts={hideKeyboardShortcuts}
        onCloseUpgradeModal={() => handleNavigationClick('/pricing')}
        onCloseFeedback={() => {}} // TODO: Implementar feedback
        onCloseTutorial={handleCloseTutorial}
        onNextTutorialStep={handleNextTutorialStep}
        onUpgradeProClick={handleUpgradeProClick}
        onTrialClick={handleTrialClick}
      />
    </div>
  );
}

/**
 * Editor de planilha refatorado com suporte a comandos por IA
 */
export function SpreadsheetEditorRefactored({
  workbookId,
  initialData,
  readOnly = false,
  onSave,
  initialCommand,
}: SpreadsheetEditorProps) {
  return (
    <SpreadsheetProvider
      initialData={initialData}
      workbookId={workbookId}
      readOnly={readOnly}
    >
      <SpreadsheetEditorInner
        workbookId={workbookId}
        onSave={onSave}
        initialCommand={initialCommand}
      />
    </SpreadsheetProvider>
  );
}
