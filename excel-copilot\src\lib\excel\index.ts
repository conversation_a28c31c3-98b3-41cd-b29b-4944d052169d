// Re-export all Excel functionality from modularized files
export * from './types';
export * from './fileOperations';
import { normalizeCommand } from '@/lib/utils';

import { executeOperation, executeOperationsSequential } from './executionOperations';
// Evitando importar e re-exportar diretamente para evitar conflitos
import {
  normalizeOperation as normalizeOp,
  adaptOperation,
  convertToBaseOperation as convertToBaseOperationUtils,
} from './operationUtils';
import {
  extractFormulaOperations,
  extractFilterOperations,
  extractSortOperations,
  extractChartOperations,
} from './parserOperations';

// Analytics removido - módulo não disponível
// Removendo re-export que causa conflito
// export * from './operationUtils';

export {
  executeOperation,
  executeOperationsSequential,
  normalizeOp as normalizeOperation,
  adaptOperation,
  convertToBaseOperationUtils,
  normalizeCommand,
  extractFormulaOperations,
  extractFilterOperations,
  extractSortOperations,
  extractChartOperations,
};
