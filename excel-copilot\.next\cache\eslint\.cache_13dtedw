[{"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\admin\\health\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\admin\\security-stats\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\admin\\subscription-integrity\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\ai\\status\\route.ts": "4", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\analytics\\vitals\\route.ts": "5", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\api-docs\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\capture-oauth-error\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\check-env\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug\\route.ts": "9", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-flow\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-google\\route.ts": "11", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-oauth\\route.ts": "12", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-providers\\route.ts": "13", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\health\\route.ts": "14", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\reset-rate-limit\\route.ts": "15", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-config\\route.ts": "16", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-google\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-login\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-providers\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth-callback\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\billing\\customer-portal\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\chat\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\checkout\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\checkout\\trial\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\csrf\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\db-status\\route.ts": "27", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\deprecated-usage\\route.ts": "28", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\excel\\ai-process\\route.ts": "29", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\excel\\download\\[id]\\route.ts": "30", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\excel\\route.ts": "31", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\feedback\\route.ts": "32", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\issues\\route.ts": "33", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\repositories\\route.ts": "34", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\status\\route.ts": "35", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\workflows\\route.ts": "36", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\ai\\route.ts": "37", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\all\\route.ts": "38", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\auth\\route.ts": "39", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\database\\route.ts": "40", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\db\\route.ts": "41", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\debug\\route.ts": "42", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\mcp\\route.ts": "43", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\metrics\\route.ts": "44", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\route.ts": "45", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\stripe\\route.ts": "46", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\test\\route.ts": "47", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\init.ts": "48", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\legacy-redirect\\[...path]\\route.ts": "49", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\linear\\issues\\route.ts": "50", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\linear\\status\\route.ts": "51", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\linear\\teams\\route.ts": "52", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\metrics\\route.ts": "53", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\migration-example\\route.ts": "54", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\socket\\route.ts": "55", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\customers\\route.ts": "56", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\payments\\route.ts": "57", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\status\\route.ts": "58", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\subscriptions\\route.ts": "59", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\supabase\\status\\route.ts": "60", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\supabase\\storage\\route.ts": "61", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\supabase\\tables\\route.ts": "62", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\trpc\\[trpc]\\route.ts": "63", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\user\\api-usage\\route.ts": "64", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\user\\subscription\\route.ts": "65", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\vercel\\deployments\\route.ts": "66", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\vercel\\env\\route.ts": "67", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\vercel\\status\\route.ts": "68", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\webhooks\\stripe\\route.ts": "69", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbook\\save\\route.ts": "70", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\recent\\route.ts": "71", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\route.ts": "72", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\shared\\route.ts": "73", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\collaborators\\route.ts": "74", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\duplicate\\route.ts": "75", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\export\\route.ts": "76", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\route.ts": "77", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\sheets\\[sheetId]\\chunks\\route.ts": "78", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\storage\\route.ts": "79", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\ws\\route.ts": "80", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\auth\\signin\\page.tsx": "81", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\dashboard\\account\\page.tsx": "82", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\dashboard\\analytics\\page.tsx": "83", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\dashboard\\page.tsx": "84", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\examples\\page.tsx": "85", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx": "86", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx": "87", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\page.tsx": "88", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\pricing\\layout.tsx": "89", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\pricing\\page.tsx": "90", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\privacy\\page.tsx": "91", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\providers.tsx": "92", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\robots.ts": "93", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\sitemap.ts": "94", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\templates\\page.tsx": "95", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\terms\\page.tsx": "96", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\new\\layout.tsx": "97", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\new\\page.tsx": "98", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\[id]\\page.tsx": "99", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ai-status-indicator.tsx": "100", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\AppInitializer.tsx": "101", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\billing\\customer-portal.tsx": "102", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\billing\\stripe-script-provider.tsx": "103", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chart-display-lazy.tsx": "104", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chart-display.tsx": "105", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\chat-interface.tsx": "106", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\chat-message.tsx": "107", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\ChatInput.tsx": "108", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\CommandPalette.tsx": "109", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\empty-state.tsx": "110", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\index.tsx": "111", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\message-content.tsx": "112", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\operations-indicator.tsx": "113", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\types.ts": "114", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\client-scripts.tsx": "115", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ClientLayoutWrapper.tsx": "116", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ClientOnly.tsx": "117", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\collaboration-indicator.tsx": "118", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\collaboration-panel\\index.tsx": "119", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\collaboration-panel.tsx": "120", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\command-examples-wrapper.tsx": "121", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\command-examples.tsx": "122", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\command-feedback.tsx": "123", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\command-preview.tsx": "124", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\command-selector.tsx": "125", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\create-workbook-form.tsx": "126", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\dashboard\\QuickActions.tsx": "127", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\dashboard\\RecentActivity.tsx": "128", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\dashboard\\WorkbooksTable.tsx": "129", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\debug\\PerformanceDebugPanel.tsx": "130", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\enhanced-chat-input.tsx": "131", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\examples\\AnimationWrapperExample.tsx": "132", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\examples\\MotionSafeExample.tsx": "133", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\export-button.tsx": "134", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\feature-card.tsx": "135", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\hero-section.tsx": "136", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\icons\\index.ts": "137", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ImageWithFallback.tsx": "138", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\locale-switcher.tsx": "139", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\mode-toggle.tsx": "140", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\nav-bar.tsx": "141", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\providers\\csrf-provider.tsx": "142", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\realtime\\OnlineUsers.tsx": "143", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\rsc-error-suppressor.tsx": "144", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\settings-button.tsx": "145", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\theme-provider.tsx": "146", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\theme-toggle.tsx": "147", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\trpc-demo.tsx": "148", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\action-button.tsx": "149", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\alert-dialog.tsx": "150", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\alert.tsx": "151", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\animation-wrapper.tsx": "152", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\avatar.tsx": "153", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\badge.tsx": "154", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\button.tsx": "155", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\card-grid.tsx": "156", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\card.tsx": "157", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\checkbox.tsx": "158", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\dialog.tsx": "159", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\dropdown-menu.tsx": "160", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\empty-state.tsx": "161", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\error-message.tsx": "162", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\form-field-styles.ts": "163", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\index.ts": "164", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\input.tsx": "165", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\label.tsx": "166", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\loading-indicator.tsx": "167", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\loading-optimization.tsx": "168", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\motion-safe.tsx": "169", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\motion.tsx": "170", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\optimized-button.tsx": "171", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\popover.tsx": "172", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\progress.tsx": "173", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\scroll-area.tsx": "174", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\select.tsx": "175", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\separator.tsx": "176", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\sheet.tsx": "177", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\skeleton.tsx": "178", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\spinner.tsx": "179", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\switch.tsx": "180", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\tab-button.tsx": "181", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\table.tsx": "182", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\tabs.tsx": "183", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\textarea.tsx": "184", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\theme-toggle\\index.ts": "185", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\theme-toggle.tsx": "186", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\toast.tsx": "187", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\toaster.tsx": "188", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\tooltip.tsx": "189", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\use-toast.ts": "190", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\virtual-table.tsx": "191", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\visually-hidden.tsx": "192", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\upload-button.tsx": "193", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\user-nav.tsx": "194", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\user-onboarding\\ActiveTour.tsx": "195", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\user-onboarding\\TourProvider.tsx": "196", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\user-onboarding\\TourStep.tsx": "197", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\AIPanel.tsx": "198", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\components\\AIAssistantPanel.tsx": "199", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\components\\MobileChat.tsx": "200", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\components\\SpreadsheetGrid.tsx": "201", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\components\\SpreadsheetModals.tsx": "202", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\components\\SpreadsheetToolbar.tsx": "203", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\hooks\\useSpreadsheetData.ts": "204", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\hooks\\useSpreadsheetKeyboard.ts": "205", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\hooks\\useSpreadsheetUI.ts": "206", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\index.ts": "207", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\OptimizedTableComponents.tsx": "208", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\QuickCommands.tsx": "209", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\SpreadsheetContext.tsx": "210", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\SpreadsheetEditor.tsx": "211", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\SpreadsheetEditorRefactored.tsx": "212", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\SpreadsheetToolbar.tsx": "213", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook-actions.tsx": "214", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook-list-item.tsx": "215", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook-templates.tsx": "216", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\ai-prompts.ts": "217", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\auth-flags.ts": "218", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\i18n.ts": "219", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\mcp-config.ts": "220", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\security-validator.ts": "221", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\site.ts": "222", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\unified-environment.ts": "223", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\context-providers\\index.ts": "224", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\context-providers\\LocaleProvider.tsx": "225", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\context-providers\\ProviderComposer.tsx": "226", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\context-providers\\ThemeProvider.tsx": "227", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\contexts\\LocaleContext.tsx": "228", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\index.ts": "229", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\use-media-query.ts": "230", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\use-media-query.tsx": "231", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useAIChat.ts": "232", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useAnalytics.ts": "233", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useBidirectionalVirtualizer.ts": "234", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useChunkedData.ts": "235", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useChunkedSpreadsheet.ts": "236", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useCleanup.ts": "237", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useErrorHandler.ts": "238", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useExcelFile.ts": "239", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useExcelOperations.ts": "240", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useExcelWorker.ts": "241", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useFileOperation.ts": "242", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useI18n.tsx": "243", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useKeyboardShortcut.ts": "244", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useReducedMotion.ts": "245", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useSocket.ts": "246", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useSSRSafeSession.ts": "247", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useSupabaseStorage.ts": "248", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useTranslation.ts": "249", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useWorkbookRealtime.ts": "250", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\ai-adapter.ts": "251", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\ai-factory.ts": "252", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\client-polyfill.ts": "253", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\client-safe.ts": "254", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\conditional-import.ts": "255", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\constants.ts": "256", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\dynamic-import.ts": "257", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\error-interceptor.ts": "258", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\excel-desktop-connector.ts": "259", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\ExcelAIProcessor.ts": "260", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\feedback-service.ts": "261", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\gemini-api.ts": "262", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\gemini-service.ts": "263", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\index.ts": "264", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\mock-vertex-service.ts": "265", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\prompts.ts": "266", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\provider.ts": "267", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\regex-test.ts": "268", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\simple-error-suppressor.ts": "269", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\types.ts": "270", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\webpack-interceptor.ts": "271", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\animations.ts": "272", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\api-tracker.ts": "273", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\api-usage.ts": "274", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\app-initializer.ts": "275", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth\\security.ts": "276", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth\\validation.ts": "277", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth-audit-logger.ts": "278", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth-config.ts": "279", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth-monitoring.ts": "280", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth.ts": "281", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\cache\\ai-command-cache.ts": "282", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\cache\\cache-manager.ts": "283", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\cache\\redis-client.ts": "284", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\cache-manager.ts": "285", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\chartOperations.ts": "286", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\chunk-manager.ts": "287", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\collaboration\\store.ts": "288", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\collaborative-sync.ts": "289", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\critical-css.ts": "290", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\design-tokens.ts": "291", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\edge-logger.ts": "292", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\env-validator.ts": "293", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\error-handler.ts": "294", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\error-reporting.ts": "295", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\errors.ts": "296", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\executionOperations.ts": "297", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\fileOperations.ts": "298", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\index.ts": "299", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\operationUtils.ts": "300", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\parserOperations.ts": "301", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\testUtils.ts": "302", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\types.ts": "303", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\typeUtils.ts": "304", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel.ts": "305", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\fallback-handlers.ts": "306", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\github-integration.ts": "307", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checker.ts": "308", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\ai.ts": "309", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\auth.ts": "310", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\database.ts": "311", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\index.ts": "312", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\mcp.ts": "313", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\stripe.ts": "314", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks.ts": "315", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\index.ts": "316", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\linear-integration.ts": "317", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\logger.ts": "318", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\mcp-tools.ts": "319", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\memory-monitor.ts": "320", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\oauth-limiter.ts": "321", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\payment-limiter.ts": "322", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\plan-based-rate-limiter-secure.ts": "323", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\plan-based-rate-limiter.ts": "324", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\rate-limiter.ts": "325", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\withErrorHandling.ts": "326", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\withServerValidation.ts": "327", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\mock-db.ts": "328", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\advancedChartOperations.ts": "329", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\advancedVisualizationOperations.ts": "330", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\cellOperations.ts": "331", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\chartOperations.ts": "332", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\columnOperations.ts": "333", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\conditionalFormattingOperations.ts": "334", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\dataTransformations.ts": "335", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\filterOperations.ts": "336", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\formatOperations.ts": "337", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\formulaOperations.ts": "338", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\index.ts": "339", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\pivotTableOperations.ts": "340", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\processor.ts": "341", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\tableOperations.ts": "342", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\utils.ts": "343", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\patches.js": "344", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\performance-monitor.ts": "345", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\prisma.ts": "346", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\rate-limiter.ts": "347", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\rate-limiting\\oauth-rate-limiter.ts": "348", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\csrf-protection.ts": "349", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\edge-csrf.ts": "350", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\enhanced-rate-limiter.ts": "351", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\pattern-detection.ts": "352", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\rate-limiter.ts": "353", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\sanitization-excel.ts": "354", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\sanitization.ts": "355", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\sanitize-html.ts": "356", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security-monitor.ts": "357", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\services.ts": "358", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\session-helpers.ts": "359", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\stripe-integration.ts": "360", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\stripe.ts": "361", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\subscription-limits.ts": "362", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\supabase\\client.ts": "363", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\supabase\\realtime.ts": "364", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\supabase\\storage.ts": "365", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\supabase-integration.ts": "366", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\trpc.tsx": "367", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\utils\\empty-module.js": "368", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\utils\\error-utils.ts": "369", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\utils\\rate-limit.ts": "370", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\utils\\request.ts": "371", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\utils.ts": "372", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\validators\\workbook.ts": "373", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\vercel-integration.ts": "374", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\middleware\\auth-callback-fix.ts": "375", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\middleware\\auth.ts": "376", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\middleware\\core.ts": "377", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\middleware\\metrics.ts": "378", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\middleware\\rate-limit.ts": "379", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\providers\\toast-wrapper.tsx": "380", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\schemas\\socket.ts": "381", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\schemas\\workbook.ts": "382", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\ai\\base-ai-service.ts": "383", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\ai\\gemini-service.ts": "384", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\ai\\vertex-ai-service.ts": "385", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\api\\root.ts": "386", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\api-usage.ts": "387", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\auth\\options.ts": "388", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\db\\client.ts": "389", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\db\\edge-client.ts": "390", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\db\\query-cache.ts": "391", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\db\\universal-client.ts": "392", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\db\\utils.ts": "393", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\init.ts": "394", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\services\\workbook-service.ts": "395", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\test-utils\\mock-server.ts": "396", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\trpc\\init-procedure.ts": "397", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\trpc\\react.tsx": "398", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\trpc\\router.ts": "399", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\trpc\\trpc.ts": "400", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\websocket.ts": "401", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\workbook.actions.ts": "402", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\ai-processing.d.ts": "403", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\ai.d.ts": "404", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\analytics.ts": "405", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\api.d.ts": "406", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\bridge.d.ts": "407", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\chart-extensions.ts": "408", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\collaboration.d.ts": "409", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\component-extensions.d.ts": "410", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\component-fixes.d.ts": "411", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\component-props.ts": "412", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\custom.d.ts": "413", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\dashboard.ts": "414", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\errors.d.ts": "415", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\events.d.ts": "416", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\excel-unified.ts": "417", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\form-event-extensions.d.ts": "418", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\form-events.d.ts": "419", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\global-types.ts": "420", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\global.d.ts": "421", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\gtag.d.ts": "422", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\index.ts": "423", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\interfaces.ts": "424", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\jest.d.ts": "425", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\next-auth.d.ts": "426", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\optional-types.d.ts": "427", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\optional-types.ts": "428", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\prisma-extensions.d.ts": "429", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\regex-types.d.ts": "430", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\regex-types.ts": "431", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\service-extensions.d.ts": "432", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\shared.d.ts": "433", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\sheet.ts": "434", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\socket.d.ts": "435", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\test-types.d.ts": "436", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\three-environment.ts": "437", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\trpc.d.ts": "438", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\ui-components.d.ts": "439", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\ui-components.ts": "440", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\vertex-ai.d.ts": "441", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\api-response.ts": "442", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\data\\json.ts": "443", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\data-access.ts": "444", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\error-utils.ts": "445", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\excel-utils.ts": "446", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\functions\\index.ts": "447", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\http\\api-response.ts": "448", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\index.ts": "449", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\json.ts": "450", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\logger-utils.ts": "451", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\optional-helpers.ts": "452", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\performance-monitor.tsx": "453", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\regex-utils.ts": "454", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\route-migration.ts": "455", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\safe-access.ts": "456", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\type-helpers.ts": "457", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\usage-example.ts": "458", "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\workers\\excel-operations.worker.ts": "459"}, {"size": 9796, "mtime": 1750121523388, "results": "460", "hashOfConfig": "461"}, {"size": 8746, "mtime": 1750121523389, "results": "462", "hashOfConfig": "461"}, {"size": 9597, "mtime": 1748454108700, "results": "463", "hashOfConfig": "461"}, {"size": 1146, "mtime": 1750121523389, "results": "464", "hashOfConfig": "461"}, {"size": 5065, "mtime": 1750186638859, "results": "465", "hashOfConfig": "461"}, {"size": 7453, "mtime": 1748220473092, "results": "466", "hashOfConfig": "461"}, {"size": 6061, "mtime": 1750121523392, "results": "467", "hashOfConfig": "461"}, {"size": 3103, "mtime": 1750121523392, "results": "468", "hashOfConfig": "461"}, {"size": 2949, "mtime": 1750121523395, "results": "469", "hashOfConfig": "461"}, {"size": 6768, "mtime": 1750121523393, "results": "470", "hashOfConfig": "461"}, {"size": 1558, "mtime": 1750121523394, "results": "471", "hashOfConfig": "461"}, {"size": 3704, "mtime": 1750121523394, "results": "472", "hashOfConfig": "461"}, {"size": 4843, "mtime": 1750121523395, "results": "473", "hashOfConfig": "461"}, {"size": 8465, "mtime": 1750121523396, "results": "474", "hashOfConfig": "461"}, {"size": 2629, "mtime": 1750121523396, "results": "475", "hashOfConfig": "461"}, {"size": 12369, "mtime": 1750121523400, "results": "476", "hashOfConfig": "461"}, {"size": 3592, "mtime": 1750121523401, "results": "477", "hashOfConfig": "461"}, {"size": 4214, "mtime": 1750121523402, "results": "478", "hashOfConfig": "461"}, {"size": 6864, "mtime": 1750121523402, "results": "479", "hashOfConfig": "461"}, {"size": 1380, "mtime": 1748887850145, "results": "480", "hashOfConfig": "461"}, {"size": 1354, "mtime": 1748257769648, "results": "481", "hashOfConfig": "461"}, {"size": 2342, "mtime": 1750121523403, "results": "482", "hashOfConfig": "461"}, {"size": 3893, "mtime": 1750121523405, "results": "483", "hashOfConfig": "461"}, {"size": 3398, "mtime": 1750121523409, "results": "484", "hashOfConfig": "461"}, {"size": 3946, "mtime": 1750121523410, "results": "485", "hashOfConfig": "461"}, {"size": 1897, "mtime": 1750121523411, "results": "486", "hashOfConfig": "461"}, {"size": 2769, "mtime": 1748450759031, "results": "487", "hashOfConfig": "461"}, {"size": 1807, "mtime": 1750121523419, "results": "488", "hashOfConfig": "461"}, {"size": 4276, "mtime": 1748621876464, "results": "489", "hashOfConfig": "461"}, {"size": 5113, "mtime": 1747527576907, "results": "490", "hashOfConfig": "461"}, {"size": 7768, "mtime": 1748220661692, "results": "491", "hashOfConfig": "461"}, {"size": 1029, "mtime": 1746800035670, "results": "492", "hashOfConfig": "461"}, {"size": 6501, "mtime": 1750121523420, "results": "493", "hashOfConfig": "461"}, {"size": 6528, "mtime": 1750121523420, "results": "494", "hashOfConfig": "461"}, {"size": 4608, "mtime": 1750121523421, "results": "495", "hashOfConfig": "461"}, {"size": 7733, "mtime": 1750121523422, "results": "496", "hashOfConfig": "461"}, {"size": 1881, "mtime": 1750121523422, "results": "497", "hashOfConfig": "461"}, {"size": 6451, "mtime": 1750121523423, "results": "498", "hashOfConfig": "461"}, {"size": 1923, "mtime": 1750121523424, "results": "499", "hashOfConfig": "461"}, {"size": 1929, "mtime": 1750121523424, "results": "500", "hashOfConfig": "461"}, {"size": 2106, "mtime": 1748451054282, "results": "501", "hashOfConfig": "461"}, {"size": 3952, "mtime": 1750121523425, "results": "502", "hashOfConfig": "461"}, {"size": 1919, "mtime": 1750121523425, "results": "503", "hashOfConfig": "461"}, {"size": 8119, "mtime": 1750122799505, "results": "504", "hashOfConfig": "461"}, {"size": 3530, "mtime": 1750121523426, "results": "505", "hashOfConfig": "461"}, {"size": 1904, "mtime": 1750121523427, "results": "506", "hashOfConfig": "461"}, {"size": 1667, "mtime": 1750121523428, "results": "507", "hashOfConfig": "461"}, {"size": 2212, "mtime": 1750121523428, "results": "508", "hashOfConfig": "461"}, {"size": 2215, "mtime": 1747597624269, "results": "509", "hashOfConfig": "461"}, {"size": 5448, "mtime": 1748650901359, "results": "510", "hashOfConfig": "461"}, {"size": 3731, "mtime": 1748618507621, "results": "511", "hashOfConfig": "461"}, {"size": 2875, "mtime": 1748740151040, "results": "512", "hashOfConfig": "461"}, {"size": 1841, "mtime": 1748451054282, "results": "513", "hashOfConfig": "461"}, {"size": 2347, "mtime": 1748220690700, "results": "514", "hashOfConfig": "461"}, {"size": 9610, "mtime": 1750121523439, "results": "515", "hashOfConfig": "461"}, {"size": 7417, "mtime": 1748632849540, "results": "516", "hashOfConfig": "461"}, {"size": 14110, "mtime": 1748700779716, "results": "517", "hashOfConfig": "461"}, {"size": 4569, "mtime": 1750121523439, "results": "518", "hashOfConfig": "461"}, {"size": 9647, "mtime": 1748698796442, "results": "519", "hashOfConfig": "461"}, {"size": 6476, "mtime": 1748651539803, "results": "520", "hashOfConfig": "461"}, {"size": 12718, "mtime": 1748744669639, "results": "521", "hashOfConfig": "461"}, {"size": 9102, "mtime": 1748654504189, "results": "522", "hashOfConfig": "461"}, {"size": 559, "mtime": 1748450732530, "results": "523", "hashOfConfig": "461"}, {"size": 2585, "mtime": 1748451054282, "results": "524", "hashOfConfig": "461"}, {"size": 3287, "mtime": 1748451054282, "results": "525", "hashOfConfig": "461"}, {"size": 6648, "mtime": 1750121523440, "results": "526", "hashOfConfig": "461"}, {"size": 12593, "mtime": 1750121523440, "results": "527", "hashOfConfig": "461"}, {"size": 4473, "mtime": 1750121523441, "results": "528", "hashOfConfig": "461"}, {"size": 13530, "mtime": 1750121523442, "results": "529", "hashOfConfig": "461"}, {"size": 4216, "mtime": 1750121523443, "results": "530", "hashOfConfig": "461"}, {"size": 4520, "mtime": 1750121523443, "results": "531", "hashOfConfig": "461"}, {"size": 8017, "mtime": 1750121523444, "results": "532", "hashOfConfig": "461"}, {"size": 6835, "mtime": 1750121523444, "results": "533", "hashOfConfig": "461"}, {"size": 5008, "mtime": 1747494820604, "results": "534", "hashOfConfig": "461"}, {"size": 2461, "mtime": 1747498517818, "results": "535", "hashOfConfig": "461"}, {"size": 5838, "mtime": 1746915341392, "results": "536", "hashOfConfig": "461"}, {"size": 5507, "mtime": 1748451054282, "results": "537", "hashOfConfig": "461"}, {"size": 4373, "mtime": 1747604402175, "results": "538", "hashOfConfig": "461"}, {"size": 6820, "mtime": 1748448154495, "results": "539", "hashOfConfig": "461"}, {"size": 9413, "mtime": 1748220850093, "results": "540", "hashOfConfig": "461"}, {"size": 9490, "mtime": 1750121523445, "results": "541", "hashOfConfig": "461"}, {"size": 4098, "mtime": 1748221108738, "results": "542", "hashOfConfig": "461"}, {"size": 7710, "mtime": 1747527603536, "results": "543", "hashOfConfig": "461"}, {"size": 35637, "mtime": 1750223893988, "results": "544", "hashOfConfig": "461"}, {"size": 1576, "mtime": 1746490500079, "results": "545", "hashOfConfig": "461"}, {"size": 9203, "mtime": 1750121523447, "results": "546", "hashOfConfig": "461"}, {"size": 1139, "mtime": 1748892734372, "results": "547", "hashOfConfig": "461"}, {"size": 12159, "mtime": 1746358119221, "results": "548", "hashOfConfig": "461"}, {"size": 663, "mtime": 1748221294688, "results": "549", "hashOfConfig": "461"}, {"size": 25056, "mtime": 1748223989531, "results": "550", "hashOfConfig": "461"}, {"size": 4318, "mtime": 1746800035670, "results": "551", "hashOfConfig": "461"}, {"size": 4903, "mtime": 1750122495063, "results": "552", "hashOfConfig": "461"}, {"size": 542, "mtime": 1746358119221, "results": "553", "hashOfConfig": "461"}, {"size": 2028, "mtime": 1746358119221, "results": "554", "hashOfConfig": "461"}, {"size": 123, "mtime": 1746269184243, "results": "555", "hashOfConfig": "461"}, {"size": 6414, "mtime": 1746796943944, "results": "556", "hashOfConfig": "461"}, {"size": 323, "mtime": 1746269184280, "results": "557", "hashOfConfig": "461"}, {"size": 3069, "mtime": 1747394355356, "results": "558", "hashOfConfig": "461"}, {"size": 3615, "mtime": 1750121523450, "results": "559", "hashOfConfig": "461"}, {"size": 4297, "mtime": 1747491497561, "results": "560", "hashOfConfig": "461"}, {"size": 14129, "mtime": 1748868254164, "results": "561", "hashOfConfig": "461"}, {"size": 6166, "mtime": 1746490500079, "results": "562", "hashOfConfig": "461"}, {"size": 3851, "mtime": 1750121523451, "results": "563", "hashOfConfig": "461"}, {"size": 899, "mtime": 1746269861881, "results": "564", "hashOfConfig": "461"}, {"size": 11400, "mtime": 1748257806511, "results": "565", "hashOfConfig": "461"}, {"size": 17181, "mtime": 1750259642331, "results": "566", "hashOfConfig": "461"}, {"size": 2912, "mtime": 1747527642340, "results": "567", "hashOfConfig": "461"}, {"size": 11335, "mtime": 1747527579751, "results": "568", "hashOfConfig": "461"}, {"size": 10213, "mtime": 1747527579801, "results": "569", "hashOfConfig": "461"}, {"size": 14592, "mtime": 1748222088184, "results": "570", "hashOfConfig": "461"}, {"size": 219, "mtime": 1746269184592, "results": "571", "hashOfConfig": "461"}, {"size": 3145, "mtime": 1747399214200, "results": "572", "hashOfConfig": "461"}, {"size": 722, "mtime": 1746269861881, "results": "573", "hashOfConfig": "461"}, {"size": 1350, "mtime": 1746914416463, "results": "574", "hashOfConfig": "461"}, {"size": 4510, "mtime": 1748887850161, "results": "575", "hashOfConfig": "461"}, {"size": 2933, "mtime": 1748257826076, "results": "576", "hashOfConfig": "461"}, {"size": 568, "mtime": 1750121523451, "results": "577", "hashOfConfig": "461"}, {"size": 4174, "mtime": 1748222088217, "results": "578", "hashOfConfig": "461"}, {"size": 20016, "mtime": 1747527580051, "results": "579", "hashOfConfig": "461"}, {"size": 7552, "mtime": 1748259549800, "results": "580", "hashOfConfig": "461"}, {"size": 382, "mtime": 1746269861881, "results": "581", "hashOfConfig": "461"}, {"size": 12054, "mtime": 1748448154503, "results": "582", "hashOfConfig": "461"}, {"size": 4669, "mtime": 1747399021411, "results": "583", "hashOfConfig": "461"}, {"size": 2049, "mtime": 1746796231661, "results": "584", "hashOfConfig": "461"}, {"size": 549, "mtime": 1746742540310, "results": "585", "hashOfConfig": "461"}, {"size": 4911, "mtime": 1748222088262, "results": "586", "hashOfConfig": "461"}, {"size": 10947, "mtime": 1750259612293, "results": "587", "hashOfConfig": "461"}, {"size": 9072, "mtime": 1750121523454, "results": "588", "hashOfConfig": "461"}, {"size": 16383, "mtime": 1750221780219, "results": "589", "hashOfConfig": "461"}, {"size": 9737, "mtime": 1748448154508, "results": "590", "hashOfConfig": "461"}, {"size": 6216, "mtime": 1748222088305, "results": "591", "hashOfConfig": "461"}, {"size": 3866, "mtime": 1746269184898, "results": "592", "hashOfConfig": "461"}, {"size": 2105, "mtime": 1746269861881, "results": "593", "hashOfConfig": "461"}, {"size": 2275, "mtime": 1746463059211, "results": "594", "hashOfConfig": "461"}, {"size": 1504, "mtime": 1746269861881, "results": "595", "hashOfConfig": "461"}, {"size": 30144, "mtime": 1748222088305, "results": "596", "hashOfConfig": "461"}, {"size": 3172, "mtime": 1748448154513, "results": "597", "hashOfConfig": "461"}, {"size": 879, "mtime": 1746915341421, "results": "598", "hashOfConfig": "461"}, {"size": 244, "mtime": 1746269184982, "results": "599", "hashOfConfig": "461"}, {"size": 1260, "mtime": 1746269184982, "results": "600", "hashOfConfig": "461"}, {"size": 10638, "mtime": 1750122542692, "results": "601", "hashOfConfig": "461"}, {"size": 3394, "mtime": 1746269185016, "results": "602", "hashOfConfig": "461"}, {"size": 8502, "mtime": 1748448154515, "results": "603", "hashOfConfig": "461"}, {"size": 4014, "mtime": 1748887850161, "results": "604", "hashOfConfig": "461"}, {"size": 497, "mtime": 1746269861881, "results": "605", "hashOfConfig": "461"}, {"size": 1473, "mtime": 1746538713055, "results": "606", "hashOfConfig": "461"}, {"size": 1154, "mtime": 1746323453619, "results": "607", "hashOfConfig": "461"}, {"size": 4886, "mtime": 1748473779452, "results": "608", "hashOfConfig": "461"}, {"size": 1575, "mtime": 1746463059216, "results": "609", "hashOfConfig": "461"}, {"size": 4486, "mtime": 1746269861881, "results": "610", "hashOfConfig": "461"}, {"size": 1791, "mtime": 1746269861881, "results": "611", "hashOfConfig": "461"}, {"size": 3307, "mtime": 1746269861881, "results": "612", "hashOfConfig": "461"}, {"size": 1459, "mtime": 1746269861881, "results": "613", "hashOfConfig": "461"}, {"size": 1230, "mtime": 1746269861881, "results": "614", "hashOfConfig": "461"}, {"size": 4812, "mtime": 1746269861881, "results": "615", "hashOfConfig": "461"}, {"size": 815, "mtime": 1746269861881, "results": "616", "hashOfConfig": "461"}, {"size": 3474, "mtime": 1746269861881, "results": "617", "hashOfConfig": "461"}, {"size": 1095, "mtime": 1746269861881, "results": "618", "hashOfConfig": "461"}, {"size": 3902, "mtime": 1746269861881, "results": "619", "hashOfConfig": "461"}, {"size": 7489, "mtime": 1747482572499, "results": "620", "hashOfConfig": "461"}, {"size": 1050, "mtime": 1746269861881, "results": "621", "hashOfConfig": "461"}, {"size": 2855, "mtime": 1748474824419, "results": "622", "hashOfConfig": "461"}, {"size": 2502, "mtime": 1748448154519, "results": "623", "hashOfConfig": "461"}, {"size": 991, "mtime": 1748627290000, "results": "624", "hashOfConfig": "461"}, {"size": 1106, "mtime": 1748627108407, "results": "625", "hashOfConfig": "461"}, {"size": 736, "mtime": 1746269861881, "results": "626", "hashOfConfig": "461"}, {"size": 820, "mtime": 1746269185249, "results": "627", "hashOfConfig": "461"}, {"size": 2764, "mtime": 1746991732904, "results": "628", "hashOfConfig": "461"}, {"size": 1271, "mtime": 1746269861881, "results": "629", "hashOfConfig": "461"}, {"size": 4911, "mtime": 1748222088325, "results": "630", "hashOfConfig": "461"}, {"size": 4020, "mtime": 1748448154519, "results": "631", "hashOfConfig": "461"}, {"size": 1284, "mtime": 1746269861881, "results": "632", "hashOfConfig": "461"}, {"size": 805, "mtime": 1746269861881, "results": "633", "hashOfConfig": "461"}, {"size": 1686, "mtime": 1746269861881, "results": "634", "hashOfConfig": "461"}, {"size": 5767, "mtime": 1746269861881, "results": "635", "hashOfConfig": "461"}, {"size": 746, "mtime": 1748448154519, "results": "636", "hashOfConfig": "461"}, {"size": 4348, "mtime": 1746269861881, "results": "637", "hashOfConfig": "461"}, {"size": 241, "mtime": 1746269185417, "results": "638", "hashOfConfig": "461"}, {"size": 1030, "mtime": 1746269861881, "results": "639", "hashOfConfig": "461"}, {"size": 1188, "mtime": 1746269861881, "results": "640", "hashOfConfig": "461"}, {"size": 1465, "mtime": 1746463059216, "results": "641", "hashOfConfig": "461"}, {"size": 2859, "mtime": 1746269861881, "results": "642", "hashOfConfig": "461"}, {"size": 1964, "mtime": 1746269861881, "results": "643", "hashOfConfig": "461"}, {"size": 1105, "mtime": 1748448154535, "results": "644", "hashOfConfig": "461"}, {"size": 47, "mtime": 1748222306384, "results": "645", "hashOfConfig": "461"}, {"size": 5735, "mtime": 1748271208107, "results": "646", "hashOfConfig": "461"}, {"size": 4989, "mtime": 1746269861881, "results": "647", "hashOfConfig": "461"}, {"size": 799, "mtime": 1746269185540, "results": "648", "hashOfConfig": "461"}, {"size": 1182, "mtime": 1746269861881, "results": "649", "hashOfConfig": "461"}, {"size": 4515, "mtime": 1748259492059, "results": "650", "hashOfConfig": "461"}, {"size": 5736, "mtime": 1746915341422, "results": "651", "hashOfConfig": "461"}, {"size": 493, "mtime": 1746269861881, "results": "652", "hashOfConfig": "461"}, {"size": 3349, "mtime": 1748448154535, "results": "653", "hashOfConfig": "461"}, {"size": 2910, "mtime": 1746462994145, "results": "654", "hashOfConfig": "461"}, {"size": 1411, "mtime": 1747495341319, "results": "655", "hashOfConfig": "461"}, {"size": 7153, "mtime": 1746915341422, "results": "656", "hashOfConfig": "461"}, {"size": 6690, "mtime": 1746463059216, "results": "657", "hashOfConfig": "461"}, {"size": 6533, "mtime": 1750121523455, "results": "658", "hashOfConfig": "461"}, {"size": 6896, "mtime": 1750223388462, "results": "659", "hashOfConfig": "461"}, {"size": 3280, "mtime": 1750223405205, "results": "660", "hashOfConfig": "461"}, {"size": 8815, "mtime": 1750121523461, "results": "661", "hashOfConfig": "461"}, {"size": 8827, "mtime": 1750223875617, "results": "662", "hashOfConfig": "461"}, {"size": 7084, "mtime": 1750186683632, "results": "663", "hashOfConfig": "461"}, {"size": 7433, "mtime": 1750121523478, "results": "664", "hashOfConfig": "461"}, {"size": 6383, "mtime": 1750121523478, "results": "665", "hashOfConfig": "461"}, {"size": 5989, "mtime": 1750121523479, "results": "666", "hashOfConfig": "461"}, {"size": 1077, "mtime": 1750121523479, "results": "667", "hashOfConfig": "461"}, {"size": 10477, "mtime": 1747483357402, "results": "668", "hashOfConfig": "461"}, {"size": 2186, "mtime": 1750121523455, "results": "669", "hashOfConfig": "461"}, {"size": 9984, "mtime": 1750121523456, "results": "670", "hashOfConfig": "461"}, {"size": 54048, "mtime": 1750181844601, "results": "671", "hashOfConfig": "461"}, {"size": 7376, "mtime": 1750259670935, "results": "672", "hashOfConfig": "461"}, {"size": 2924, "mtime": 1750121523458, "results": "673", "hashOfConfig": "461"}, {"size": 4700, "mtime": 1746490500079, "results": "674", "hashOfConfig": "461"}, {"size": 1655, "mtime": 1746269861881, "results": "675", "hashOfConfig": "461"}, {"size": 10622, "mtime": 1746915341422, "results": "676", "hashOfConfig": "461"}, {"size": 5314, "mtime": 1746269185803, "results": "677", "hashOfConfig": "461"}, {"size": 1974, "mtime": 1750121523480, "results": "678", "hashOfConfig": "461"}, {"size": 7655, "mtime": 1746914399540, "results": "679", "hashOfConfig": "461"}, {"size": 15117, "mtime": 1750121523484, "results": "680", "hashOfConfig": "461"}, {"size": 8887, "mtime": 1750121523485, "results": "681", "hashOfConfig": "461"}, {"size": 430, "mtime": 1746269185852, "results": "682", "hashOfConfig": "461"}, {"size": 28037, "mtime": 1750121523486, "results": "683", "hashOfConfig": "461"}, {"size": 1013, "mtime": 1747498945190, "results": "684", "hashOfConfig": "461"}, {"size": 1836, "mtime": 1748257879045, "results": "685", "hashOfConfig": "461"}, {"size": 1096, "mtime": 1748259724754, "results": "686", "hashOfConfig": "461"}, {"size": 1194, "mtime": 1746835545411, "results": "687", "hashOfConfig": "461"}, {"size": 1899, "mtime": 1748270711329, "results": "688", "hashOfConfig": "461"}, {"size": 167, "mtime": 1746269185873, "results": "689", "hashOfConfig": "461"}, {"size": 2509, "mtime": 1748257919384, "results": "690", "hashOfConfig": "461"}, {"size": 1900, "mtime": 1746269185889, "results": "691", "hashOfConfig": "461"}, {"size": 15800, "mtime": 1750181826035, "results": "692", "hashOfConfig": "461"}, {"size": 687, "mtime": 1746269185902, "results": "693", "hashOfConfig": "461"}, {"size": 6150, "mtime": 1748257981503, "results": "694", "hashOfConfig": "461"}, {"size": 6798, "mtime": 1746914416467, "results": "695", "hashOfConfig": "461"}, {"size": 8684, "mtime": 1748222907966, "results": "696", "hashOfConfig": "461"}, {"size": 294, "mtime": 1747585530784, "results": "697", "hashOfConfig": "461"}, {"size": 14320, "mtime": 1748257999764, "results": "698", "hashOfConfig": "461"}, {"size": 6995, "mtime": 1748260427117, "results": "699", "hashOfConfig": "461"}, {"size": 12307, "mtime": 1748887850161, "results": "700", "hashOfConfig": "461"}, {"size": 7835, "mtime": 1750183980719, "results": "701", "hashOfConfig": "461"}, {"size": 1999, "mtime": 1746914416473, "results": "702", "hashOfConfig": "461"}, {"size": 1676, "mtime": 1748258035172, "results": "703", "hashOfConfig": "461"}, {"size": 2931, "mtime": 1746915341422, "results": "704", "hashOfConfig": "461"}, {"size": 1915, "mtime": 1746269186019, "results": "705", "hashOfConfig": "461"}, {"size": 4658, "mtime": 1746914416475, "results": "706", "hashOfConfig": "461"}, {"size": 721, "mtime": 1750121523490, "results": "707", "hashOfConfig": "461"}, {"size": 6930, "mtime": 1748448154542, "results": "708", "hashOfConfig": "461"}, {"size": 6903, "mtime": 1747495341962, "results": "709", "hashOfConfig": "461"}, {"size": 8611, "mtime": 1748448154542, "results": "710", "hashOfConfig": "461"}, {"size": 9140, "mtime": 1747495341980, "results": "711", "hashOfConfig": "461"}, {"size": 3221, "mtime": 1750121523491, "results": "712", "hashOfConfig": "461"}, {"size": 6188, "mtime": 1750121523492, "results": "713", "hashOfConfig": "461"}, {"size": 2628, "mtime": 1750121523492, "results": "714", "hashOfConfig": "461"}, {"size": 1817, "mtime": 1750121523493, "results": "715", "hashOfConfig": "461"}, {"size": 3036, "mtime": 1750121523493, "results": "716", "hashOfConfig": "461"}, {"size": 11811, "mtime": 1750121523494, "results": "717", "hashOfConfig": "461"}, {"size": 4643, "mtime": 1748632655152, "results": "718", "hashOfConfig": "461"}, {"size": 3104, "mtime": 1750259728381, "results": "719", "hashOfConfig": "461"}, {"size": 7160, "mtime": 1748887850161, "results": "720", "hashOfConfig": "461"}, {"size": 6724, "mtime": 1746800035670, "results": "721", "hashOfConfig": "461"}, {"size": 5151, "mtime": 1748258178821, "results": "722", "hashOfConfig": "461"}, {"size": 29544, "mtime": 1750121523495, "results": "723", "hashOfConfig": "461"}, {"size": 213, "mtime": 1748887850161, "results": "724", "hashOfConfig": "461"}, {"size": 3374, "mtime": 1747489905543, "results": "725", "hashOfConfig": "461"}, {"size": 7583, "mtime": 1746795153518, "results": "726", "hashOfConfig": "461"}, {"size": 5864, "mtime": 1748223289943, "results": "727", "hashOfConfig": "461"}, {"size": 495, "mtime": 1746800035670, "results": "728", "hashOfConfig": "461"}, {"size": 6001, "mtime": 1748824466044, "results": "729", "hashOfConfig": "461"}, {"size": 2448, "mtime": 1746269186253, "results": "730", "hashOfConfig": "461"}, {"size": 3980, "mtime": 1750121523495, "results": "731", "hashOfConfig": "461"}, {"size": 3114, "mtime": 1746269186270, "results": "732", "hashOfConfig": "461"}, {"size": 1303, "mtime": 1747527584399, "results": "733", "hashOfConfig": "461"}, {"size": 3965, "mtime": 1747496185484, "results": "734", "hashOfConfig": "461"}, {"size": 31917, "mtime": 1750122706137, "results": "735", "hashOfConfig": "461"}, {"size": 7871, "mtime": 1748887850161, "results": "736", "hashOfConfig": "461"}, {"size": 5680, "mtime": 1750121523554, "results": "737", "hashOfConfig": "461"}, {"size": 9089, "mtime": 1748455463233, "results": "738", "hashOfConfig": "461"}, {"size": 2205, "mtime": 1750121523553, "results": "739", "hashOfConfig": "461"}, {"size": 13446, "mtime": 1750121523553, "results": "740", "hashOfConfig": "461"}, {"size": 1302, "mtime": 1748448154542, "results": "741", "hashOfConfig": "461"}, {"size": 7471, "mtime": 1750121523556, "results": "742", "hashOfConfig": "461"}, {"size": 8536, "mtime": 1750121523556, "results": "743", "hashOfConfig": "461"}, {"size": 4347, "mtime": 1750122944129, "results": "744", "hashOfConfig": "461"}, {"size": 20558, "mtime": 1750121523555, "results": "745", "hashOfConfig": "461"}, {"size": 10644, "mtime": 1750223219446, "results": "746", "hashOfConfig": "461"}, {"size": 7013, "mtime": 1747489924233, "results": "747", "hashOfConfig": "461"}, {"size": 6270, "mtime": 1746269186486, "results": "748", "hashOfConfig": "461"}, {"size": 4819, "mtime": 1746991732904, "results": "749", "hashOfConfig": "461"}, {"size": 2216, "mtime": 1746269186521, "results": "750", "hashOfConfig": "461"}, {"size": 7863, "mtime": 1746269186537, "results": "751", "hashOfConfig": "461"}, {"size": 2986, "mtime": 1748459791670, "results": "752", "hashOfConfig": "461"}, {"size": 24645, "mtime": 1750121523558, "results": "753", "hashOfConfig": "461"}, {"size": 12864, "mtime": 1748258363918, "results": "754", "hashOfConfig": "461"}, {"size": 4580, "mtime": 1747485516728, "results": "755", "hashOfConfig": "461"}, {"size": 16528, "mtime": 1748258391914, "results": "756", "hashOfConfig": "461"}, {"size": 4384, "mtime": 1748258416142, "results": "757", "hashOfConfig": "461"}, {"size": 6848, "mtime": 1746914399540, "results": "758", "hashOfConfig": "461"}, {"size": 1056, "mtime": 1750259815380, "results": "759", "hashOfConfig": "461"}, {"size": 4966, "mtime": 1748258508995, "results": "760", "hashOfConfig": "461"}, {"size": 11381, "mtime": 1746915341439, "results": "761", "hashOfConfig": "461"}, {"size": 2418, "mtime": 1746269861881, "results": "762", "hashOfConfig": "461"}, {"size": 7660, "mtime": 1746638779407, "results": "763", "hashOfConfig": "461"}, {"size": 2134, "mtime": 1746269186956, "results": "764", "hashOfConfig": "461"}, {"size": 49486, "mtime": 1748223586064, "results": "765", "hashOfConfig": "461"}, {"size": 11268, "mtime": 1750259770025, "results": "766", "hashOfConfig": "461"}, {"size": 20429, "mtime": 1750121523559, "results": "767", "hashOfConfig": "461"}, {"size": 23223, "mtime": 1750121523560, "results": "768", "hashOfConfig": "461"}, {"size": 11643, "mtime": 1750121523561, "results": "769", "hashOfConfig": "461"}, {"size": 11875, "mtime": 1750121523561, "results": "770", "hashOfConfig": "461"}, {"size": 7594, "mtime": 1750121523562, "results": "771", "hashOfConfig": "461"}, {"size": 10086, "mtime": 1750122967668, "results": "772", "hashOfConfig": "461"}, {"size": 11572, "mtime": 1750121523563, "results": "773", "hashOfConfig": "461"}, {"size": 11592, "mtime": 1750121523563, "results": "774", "hashOfConfig": "461"}, {"size": 10052, "mtime": 1750121523560, "results": "775", "hashOfConfig": "461"}, {"size": 11, "mtime": 1747585516556, "results": "776", "hashOfConfig": "461"}, {"size": 20572, "mtime": 1750121523567, "results": "777", "hashOfConfig": "461"}, {"size": 15965, "mtime": 1750121523568, "results": "778", "hashOfConfig": "461"}, {"size": 9395, "mtime": 1748887850161, "results": "779", "hashOfConfig": "461"}, {"size": 1528, "mtime": 1747587181685, "results": "780", "hashOfConfig": "461"}, {"size": 6337, "mtime": 1748803094720, "results": "781", "hashOfConfig": "461"}, {"size": 8270, "mtime": 1750121523569, "results": "782", "hashOfConfig": "461"}, {"size": 12158, "mtime": 1750121523569, "results": "783", "hashOfConfig": "461"}, {"size": 9228, "mtime": 1750121523570, "results": "784", "hashOfConfig": "461"}, {"size": 8196, "mtime": 1750121523570, "results": "785", "hashOfConfig": "461"}, {"size": 14363, "mtime": 1747495343259, "results": "786", "hashOfConfig": "461"}, {"size": 14702, "mtime": 1750121523587, "results": "787", "hashOfConfig": "461"}, {"size": 5261, "mtime": 1750121523588, "results": "788", "hashOfConfig": "461"}, {"size": 21288, "mtime": 1748259163943, "results": "789", "hashOfConfig": "461"}, {"size": 11541, "mtime": 1747496185485, "results": "790", "hashOfConfig": "461"}, {"size": 7881, "mtime": 1748448154555, "results": "791", "hashOfConfig": "461"}, {"size": 10125, "mtime": 1748258622767, "results": "792", "hashOfConfig": "461"}, {"size": 11920, "mtime": 1746275281946, "results": "793", "hashOfConfig": "461"}, {"size": 26541, "mtime": 1747589875590, "results": "794", "hashOfConfig": "461"}, {"size": 11383, "mtime": 1747589853008, "results": "795", "hashOfConfig": "461"}, {"size": 16288, "mtime": 1748258641063, "results": "796", "hashOfConfig": "461"}, {"size": 14907, "mtime": 1747612628116, "results": "797", "hashOfConfig": "461"}, {"size": 11464, "mtime": 1748448154555, "results": "798", "hashOfConfig": "461"}, {"size": 2947, "mtime": 1746280756027, "results": "799", "hashOfConfig": "461"}, {"size": 18767, "mtime": 1748258681003, "results": "800", "hashOfConfig": "461"}, {"size": 2380, "mtime": 1746796725915, "results": "801", "hashOfConfig": "461"}, {"size": 10905, "mtime": 1747612642552, "results": "802", "hashOfConfig": "461"}, {"size": 4245, "mtime": 1748223320720, "results": "803", "hashOfConfig": "461"}, {"size": 1553, "mtime": 1748223645946, "results": "804", "hashOfConfig": "461"}, {"size": 5736, "mtime": 1748260205602, "results": "805", "hashOfConfig": "461"}, {"size": 476, "mtime": 1750223423246, "results": "806", "hashOfConfig": "461"}, {"size": 12077, "mtime": 1750121523601, "results": "807", "hashOfConfig": "461"}, {"size": 5542, "mtime": 1750121523602, "results": "808", "hashOfConfig": "461"}, {"size": 6123, "mtime": 1750121523602, "results": "809", "hashOfConfig": "461"}, {"size": 4817, "mtime": 1750121523603, "results": "810", "hashOfConfig": "461"}, {"size": 13478, "mtime": 1750121523603, "results": "811", "hashOfConfig": "461"}, {"size": 3053, "mtime": 1747495343802, "results": "812", "hashOfConfig": "461"}, {"size": 14054, "mtime": 1747521713872, "results": "813", "hashOfConfig": "461"}, {"size": 2780, "mtime": 1748258703194, "results": "814", "hashOfConfig": "461"}, {"size": 24503, "mtime": 1748222329127, "results": "815", "hashOfConfig": "461"}, {"size": 2952, "mtime": 1747495343918, "results": "816", "hashOfConfig": "461"}, {"size": 9069, "mtime": 1748455387468, "results": "817", "hashOfConfig": "461"}, {"size": 16084, "mtime": 1748261297416, "results": "818", "hashOfConfig": "461"}, {"size": 1779, "mtime": 1748260259319, "results": "819", "hashOfConfig": "461"}, {"size": 25958, "mtime": 1749137071445, "results": "820", "hashOfConfig": "461"}, {"size": 4960, "mtime": 1750121523605, "results": "821", "hashOfConfig": "461"}, {"size": 23598, "mtime": 1749149474580, "results": "822", "hashOfConfig": "461"}, {"size": 4985, "mtime": 1748865010225, "results": "823", "hashOfConfig": "461"}, {"size": 9402, "mtime": 1748448154555, "results": "824", "hashOfConfig": "461"}, {"size": 7900, "mtime": 1748864110352, "results": "825", "hashOfConfig": "461"}, {"size": 18580, "mtime": 1750121523605, "results": "826", "hashOfConfig": "461"}, {"size": 2600, "mtime": 1746269861881, "results": "827", "hashOfConfig": "461"}, {"size": 84, "mtime": 1748887850161, "results": "828", "hashOfConfig": "461"}, {"size": 2718, "mtime": 1748222331496, "results": "829", "hashOfConfig": "461"}, {"size": 4424, "mtime": 1746796726251, "results": "830", "hashOfConfig": "461"}, {"size": 1435, "mtime": 1747616027609, "results": "831", "hashOfConfig": "461"}, {"size": 7420, "mtime": 1746914416488, "results": "832", "hashOfConfig": "461"}, {"size": 1392, "mtime": 1746269187762, "results": "833", "hashOfConfig": "461"}, {"size": 19599, "mtime": 1748887850161, "results": "834", "hashOfConfig": "461"}, {"size": 1270, "mtime": 1748887850161, "results": "835", "hashOfConfig": "461"}, {"size": 6173, "mtime": 1750121523606, "results": "836", "hashOfConfig": "461"}, {"size": 3193, "mtime": 1750121523607, "results": "837", "hashOfConfig": "461"}, {"size": 3319, "mtime": 1746490500079, "results": "838", "hashOfConfig": "461"}, {"size": 3064, "mtime": 1746490500079, "results": "839", "hashOfConfig": "461"}, {"size": 2341, "mtime": 1747590656586, "results": "840", "hashOfConfig": "461"}, {"size": 3278, "mtime": 1746490500079, "results": "841", "hashOfConfig": "461"}, {"size": 2662, "mtime": 1750121523607, "results": "842", "hashOfConfig": "461"}, {"size": 9465, "mtime": 1748448154631, "results": "843", "hashOfConfig": "461"}, {"size": 41869, "mtime": 1750121523608, "results": "844", "hashOfConfig": "461"}, {"size": 15892, "mtime": 1750121523609, "results": "845", "hashOfConfig": "461"}, {"size": 226, "mtime": 1746796726496, "results": "846", "hashOfConfig": "461"}, {"size": 4015, "mtime": 1747495344478, "results": "847", "hashOfConfig": "461"}, {"size": 7877, "mtime": 1750121523609, "results": "848", "hashOfConfig": "461"}, {"size": 5026, "mtime": 1750121523610, "results": "849", "hashOfConfig": "461"}, {"size": 4058, "mtime": 1750121523610, "results": "850", "hashOfConfig": "461"}, {"size": 5326, "mtime": 1750121523611, "results": "851", "hashOfConfig": "461"}, {"size": 2711, "mtime": 1750121523611, "results": "852", "hashOfConfig": "461"}, {"size": 3514, "mtime": 1748448154636, "results": "853", "hashOfConfig": "461"}, {"size": 4495, "mtime": 1750121523612, "results": "854", "hashOfConfig": "461"}, {"size": 16399, "mtime": 1750123031541, "results": "855", "hashOfConfig": "461"}, {"size": 3710, "mtime": 1747489390943, "results": "856", "hashOfConfig": "461"}, {"size": 3843, "mtime": 1748448154652, "results": "857", "hashOfConfig": "461"}, {"size": 214, "mtime": 1746796726645, "results": "858", "hashOfConfig": "461"}, {"size": 11024, "mtime": 1747496185485, "results": "859", "hashOfConfig": "461"}, {"size": 4831, "mtime": 1750121523613, "results": "860", "hashOfConfig": "461"}, {"size": 3283, "mtime": 1747491715149, "results": "861", "hashOfConfig": "461"}, {"size": 5794, "mtime": 1747495344709, "results": "862", "hashOfConfig": "461"}, {"size": 2889, "mtime": 1747519484866, "results": "863", "hashOfConfig": "461"}, {"size": 1338, "mtime": 1746490500079, "results": "864", "hashOfConfig": "461"}, {"size": 510, "mtime": 1747525707221, "results": "865", "hashOfConfig": "461"}, {"size": 1324, "mtime": 1747497162344, "results": "866", "hashOfConfig": "461"}, {"size": 1501, "mtime": 1747495344747, "results": "867", "hashOfConfig": "461"}, {"size": 244, "mtime": 1746269861881, "results": "868", "hashOfConfig": "461"}, {"size": 1950, "mtime": 1747495344759, "results": "869", "hashOfConfig": "461"}, {"size": 1963, "mtime": 1747496185485, "results": "870", "hashOfConfig": "461"}, {"size": 1429, "mtime": 1748631790634, "results": "871", "hashOfConfig": "461"}, {"size": 683, "mtime": 1747401716425, "results": "872", "hashOfConfig": "461"}, {"size": 3154, "mtime": 1750181862596, "results": "873", "hashOfConfig": "461"}, {"size": 6550, "mtime": 1750121523615, "results": "874", "hashOfConfig": "461"}, {"size": 1298, "mtime": 1747495344797, "results": "875", "hashOfConfig": "461"}, {"size": 2340, "mtime": 1747495344810, "results": "876", "hashOfConfig": "461"}, {"size": 6850, "mtime": 1748448154652, "results": "877", "hashOfConfig": "461"}, {"size": 953, "mtime": 1747496185485, "results": "878", "hashOfConfig": "461"}, {"size": 1444, "mtime": 1747495344835, "results": "879", "hashOfConfig": "461"}, {"size": 4497, "mtime": 1748448154652, "results": "880", "hashOfConfig": "461"}, {"size": 8685, "mtime": 1747525991310, "results": "881", "hashOfConfig": "461"}, {"size": 379, "mtime": 1747527589733, "results": "882", "hashOfConfig": "461"}, {"size": 6735, "mtime": 1746281554348, "results": "883", "hashOfConfig": "461"}, {"size": 16727, "mtime": 1746269188127, "results": "884", "hashOfConfig": "461"}, {"size": 533, "mtime": 1747527589829, "results": "885", "hashOfConfig": "461"}, {"size": 1167, "mtime": 1750121523616, "results": "886", "hashOfConfig": "461"}, {"size": 2837, "mtime": 1748222336794, "results": "887", "hashOfConfig": "461"}, {"size": 1178, "mtime": 1748448154652, "results": "888", "hashOfConfig": "461"}, {"size": 3961, "mtime": 1747496185485, "results": "889", "hashOfConfig": "461"}, {"size": 2110, "mtime": 1748222337329, "results": "890", "hashOfConfig": "461"}, {"size": 1245, "mtime": 1747584605569, "results": "891", "hashOfConfig": "461"}, {"size": 534, "mtime": 1747495344959, "results": "892", "hashOfConfig": "461"}, {"size": 2080, "mtime": 1747496185485, "results": "893", "hashOfConfig": "461"}, {"size": 2920, "mtime": 1746835545411, "results": "894", "hashOfConfig": "461"}, {"size": 2678, "mtime": 1748448154668, "results": "895", "hashOfConfig": "461"}, {"size": 1244, "mtime": 1747527589942, "results": "896", "hashOfConfig": "461"}, {"size": 733, "mtime": 1746269188142, "results": "897", "hashOfConfig": "461"}, {"size": 1372, "mtime": 1747496185485, "results": "898", "hashOfConfig": "461"}, {"size": 3814, "mtime": 1748627262114, "results": "899", "hashOfConfig": "461"}, {"size": 1189, "mtime": 1748627279947, "results": "900", "hashOfConfig": "461"}, {"size": 1900, "mtime": 1747495345068, "results": "901", "hashOfConfig": "461"}, {"size": 2145, "mtime": 1748448154683, "results": "902", "hashOfConfig": "461"}, {"size": 3277, "mtime": 1748258776502, "results": "903", "hashOfConfig": "461"}, {"size": 9678, "mtime": 1748448154683, "results": "904", "hashOfConfig": "461"}, {"size": 6509, "mtime": 1748222339176, "results": "905", "hashOfConfig": "461"}, {"size": 8120, "mtime": 1748258796096, "results": "906", "hashOfConfig": "461"}, {"size": 3457, "mtime": 1746835545411, "results": "907", "hashOfConfig": "461"}, {"size": 3377, "mtime": 1746835545411, "results": "908", "hashOfConfig": "461"}, {"size": 2810, "mtime": 1748448154683, "results": "909", "hashOfConfig": "461"}, {"size": 2287, "mtime": 1747589411851, "results": "910", "hashOfConfig": "461"}, {"size": 3498, "mtime": 1748258819846, "results": "911", "hashOfConfig": "461"}, {"size": 2676, "mtime": 1748218063214, "results": "912", "hashOfConfig": "461"}, {"size": 7318, "mtime": 1748448154699, "results": "913", "hashOfConfig": "461"}, {"size": 5819, "mtime": 1748222339695, "results": "914", "hashOfConfig": "461"}, {"size": 7584, "mtime": 1748258843311, "results": "915", "hashOfConfig": "461"}, {"size": 2560, "mtime": 1748439476564, "results": "916", "hashOfConfig": "461"}, {"size": 4875, "mtime": 1748448154699, "results": "917", "hashOfConfig": "461"}, {"size": 3084, "mtime": 1748258868238, "results": "918", "hashOfConfig": "461"}, {"size": 6474, "mtime": 1750186784723, "results": "919", "hashOfConfig": "461"}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1n7nllt", {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1262", "messages": "1263", "suppressedMessages": "1264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1265", "messages": "1266", "suppressedMessages": "1267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1268", "messages": "1269", "suppressedMessages": "1270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1271", "messages": "1272", "suppressedMessages": "1273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1274", "messages": "1275", "suppressedMessages": "1276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1277", "messages": "1278", "suppressedMessages": "1279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1280", "messages": "1281", "suppressedMessages": "1282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1283", "messages": "1284", "suppressedMessages": "1285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1286", "messages": "1287", "suppressedMessages": "1288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1289", "messages": "1290", "suppressedMessages": "1291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1292", "messages": "1293", "suppressedMessages": "1294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1295", "messages": "1296", "suppressedMessages": "1297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 46, "fixableErrorCount": 0, "fixableWarningCount": 46, "source": null}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 54, "source": null}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1328", "messages": "1329", "suppressedMessages": "1330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1331", "messages": "1332", "suppressedMessages": "1333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1334", "messages": "1335", "suppressedMessages": "1336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1337", "messages": "1338", "suppressedMessages": "1339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1340", "messages": "1341", "suppressedMessages": "1342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1343", "messages": "1344", "suppressedMessages": "1345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1346", "messages": "1347", "suppressedMessages": "1348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1349", "messages": "1350", "suppressedMessages": "1351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1352", "messages": "1353", "suppressedMessages": "1354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1355", "messages": "1356", "suppressedMessages": "1357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1358", "messages": "1359", "suppressedMessages": "1360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1361", "messages": "1362", "suppressedMessages": "1363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1364", "messages": "1365", "suppressedMessages": "1366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1367", "messages": "1368", "suppressedMessages": "1369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1370", "messages": "1371", "suppressedMessages": "1372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1373", "messages": "1374", "suppressedMessages": "1375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1376", "messages": "1377", "suppressedMessages": "1378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1379", "messages": "1380", "suppressedMessages": "1381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1382", "messages": "1383", "suppressedMessages": "1384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1385", "messages": "1386", "suppressedMessages": "1387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1388", "messages": "1389", "suppressedMessages": "1390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1391", "messages": "1392", "suppressedMessages": "1393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1394", "messages": "1395", "suppressedMessages": "1396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1397", "messages": "1398", "suppressedMessages": "1399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1400", "messages": "1401", "suppressedMessages": "1402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1403", "messages": "1404", "suppressedMessages": "1405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1406", "messages": "1407", "suppressedMessages": "1408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1409", "messages": "1410", "suppressedMessages": "1411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1412", "messages": "1413", "suppressedMessages": "1414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1415", "messages": "1416", "suppressedMessages": "1417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1418", "messages": "1419", "suppressedMessages": "1420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1421", "messages": "1422", "suppressedMessages": "1423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1424", "messages": "1425", "suppressedMessages": "1426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1427", "messages": "1428", "suppressedMessages": "1429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1430", "messages": "1431", "suppressedMessages": "1432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1433", "messages": "1434", "suppressedMessages": "1435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1436", "messages": "1437", "suppressedMessages": "1438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1439", "messages": "1440", "suppressedMessages": "1441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1442", "messages": "1443", "suppressedMessages": "1444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1445", "messages": "1446", "suppressedMessages": "1447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1448", "messages": "1449", "suppressedMessages": "1450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1451", "messages": "1452", "suppressedMessages": "1453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1454", "messages": "1455", "suppressedMessages": "1456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1457", "messages": "1458", "suppressedMessages": "1459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1460", "messages": "1461", "suppressedMessages": "1462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1463", "messages": "1464", "suppressedMessages": "1465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1466", "messages": "1467", "suppressedMessages": "1468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1469", "messages": "1470", "suppressedMessages": "1471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1472", "messages": "1473", "suppressedMessages": "1474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1475", "messages": "1476", "suppressedMessages": "1477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1478", "messages": "1479", "suppressedMessages": "1480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1481", "messages": "1482", "suppressedMessages": "1483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1484", "messages": "1485", "suppressedMessages": "1486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1487", "messages": "1488", "suppressedMessages": "1489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1490", "messages": "1491", "suppressedMessages": "1492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1493", "messages": "1494", "suppressedMessages": "1495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1496", "messages": "1497", "suppressedMessages": "1498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1499", "messages": "1500", "suppressedMessages": "1501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1502", "messages": "1503", "suppressedMessages": "1504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1505", "messages": "1506", "suppressedMessages": "1507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1508", "messages": "1509", "suppressedMessages": "1510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1511", "messages": "1512", "suppressedMessages": "1513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1514", "messages": "1515", "suppressedMessages": "1516", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 5, "source": null}, {"filePath": "1517", "messages": "1518", "suppressedMessages": "1519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "1520", "messages": "1521", "suppressedMessages": "1522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "1523", "messages": "1524", "suppressedMessages": "1525", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 12, "source": null}, {"filePath": "1526", "messages": "1527", "suppressedMessages": "1528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "1529", "messages": "1530", "suppressedMessages": "1531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "1532", "messages": "1533", "suppressedMessages": "1534", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1535", "messages": "1536", "suppressedMessages": "1537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 9, "source": null}, {"filePath": "1538", "messages": "1539", "suppressedMessages": "1540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1541", "messages": "1542", "suppressedMessages": "1543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1544", "messages": "1545", "suppressedMessages": "1546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1547", "messages": "1548", "suppressedMessages": "1549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 42, "fixableErrorCount": 0, "fixableWarningCount": 42, "source": null}, {"filePath": "1550", "messages": "1551", "suppressedMessages": "1552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1553", "messages": "1554", "suppressedMessages": "1555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 16, "source": null}, {"filePath": "1556", "messages": "1557", "suppressedMessages": "1558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1559", "messages": "1560", "suppressedMessages": "1561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1562", "messages": "1563", "suppressedMessages": "1564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1565", "messages": "1566", "suppressedMessages": "1567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1568", "messages": "1569", "suppressedMessages": "1570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1571", "messages": "1572", "suppressedMessages": "1573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1574", "messages": "1575", "suppressedMessages": "1576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1577", "messages": "1578", "suppressedMessages": "1579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1580", "messages": "1581", "suppressedMessages": "1582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1583", "messages": "1584", "suppressedMessages": "1585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1586", "messages": "1587", "suppressedMessages": "1588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1589", "messages": "1590", "suppressedMessages": "1591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1592", "messages": "1593", "suppressedMessages": "1594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1595", "messages": "1596", "suppressedMessages": "1597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1598", "messages": "1599", "suppressedMessages": "1600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1601", "messages": "1602", "suppressedMessages": "1603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1604", "messages": "1605", "suppressedMessages": "1606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1607", "messages": "1608", "suppressedMessages": "1609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1610", "messages": "1611", "suppressedMessages": "1612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1613", "messages": "1614", "suppressedMessages": "1615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1616", "messages": "1617", "suppressedMessages": "1618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1619", "messages": "1620", "suppressedMessages": "1621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1622", "messages": "1623", "suppressedMessages": "1624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1625", "messages": "1626", "suppressedMessages": "1627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1628", "messages": "1629", "suppressedMessages": "1630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1631", "messages": "1632", "suppressedMessages": "1633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1634", "messages": "1635", "suppressedMessages": "1636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1637", "messages": "1638", "suppressedMessages": "1639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1640", "messages": "1641", "suppressedMessages": "1642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "1643", "messages": "1644", "suppressedMessages": "1645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1646", "messages": "1647", "suppressedMessages": "1648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1649", "messages": "1650", "suppressedMessages": "1651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1652", "messages": "1653", "suppressedMessages": "1654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1655", "messages": "1656", "suppressedMessages": "1657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1658", "messages": "1659", "suppressedMessages": "1660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1661", "messages": "1662", "suppressedMessages": "1663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1664", "messages": "1665", "suppressedMessages": "1666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1667", "messages": "1668", "suppressedMessages": "1669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1670", "messages": "1671", "suppressedMessages": "1672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1673", "messages": "1674", "suppressedMessages": "1675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1676", "messages": "1677", "suppressedMessages": "1678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1679", "messages": "1680", "suppressedMessages": "1681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1682", "messages": "1683", "suppressedMessages": "1684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1685", "messages": "1686", "suppressedMessages": "1687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1688", "messages": "1689", "suppressedMessages": "1690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1691", "messages": "1692", "suppressedMessages": "1693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1694", "messages": "1695", "suppressedMessages": "1696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1697", "messages": "1698", "suppressedMessages": "1699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1700", "messages": "1701", "suppressedMessages": "1702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1703", "messages": "1704", "suppressedMessages": "1705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1706", "messages": "1707", "suppressedMessages": "1708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1709", "messages": "1710", "suppressedMessages": "1711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1712", "messages": "1713", "suppressedMessages": "1714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1715", "messages": "1716", "suppressedMessages": "1717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1718", "messages": "1719", "suppressedMessages": "1720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1721", "messages": "1722", "suppressedMessages": "1723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1724", "messages": "1725", "suppressedMessages": "1726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1727", "messages": "1728", "suppressedMessages": "1729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1730", "messages": "1731", "suppressedMessages": "1732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1733", "messages": "1734", "suppressedMessages": "1735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1736", "messages": "1737", "suppressedMessages": "1738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1739", "messages": "1740", "suppressedMessages": "1741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1742", "messages": "1743", "suppressedMessages": "1744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "1745", "messages": "1746", "suppressedMessages": "1747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1748", "messages": "1749", "suppressedMessages": "1750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1751", "messages": "1752", "suppressedMessages": "1753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1754", "messages": "1755", "suppressedMessages": "1756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1757", "messages": "1758", "suppressedMessages": "1759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1760", "messages": "1761", "suppressedMessages": "1762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1763", "messages": "1764", "suppressedMessages": "1765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 27, "source": null}, {"filePath": "1766", "messages": "1767", "suppressedMessages": "1768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 17, "source": null}, {"filePath": "1769", "messages": "1770", "suppressedMessages": "1771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "1772", "messages": "1773", "suppressedMessages": "1774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1775", "messages": "1776", "suppressedMessages": "1777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1778", "messages": "1779", "suppressedMessages": "1780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1781", "messages": "1782", "suppressedMessages": "1783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1784", "messages": "1785", "suppressedMessages": "1786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1787", "messages": "1788", "suppressedMessages": "1789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1790", "messages": "1791", "suppressedMessages": "1792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1793", "messages": "1794", "suppressedMessages": "1795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1796", "messages": "1797", "suppressedMessages": "1798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1799", "messages": "1800", "suppressedMessages": "1801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1802", "messages": "1803", "suppressedMessages": "1804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1805", "messages": "1806", "suppressedMessages": "1807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1808", "messages": "1809", "suppressedMessages": "1810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1811", "messages": "1812", "suppressedMessages": "1813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1814", "messages": "1815", "suppressedMessages": "1816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1817", "messages": "1818", "suppressedMessages": "1819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1820", "messages": "1821", "suppressedMessages": "1822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1823", "messages": "1824", "suppressedMessages": "1825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1826", "messages": "1827", "suppressedMessages": "1828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1829", "messages": "1830", "suppressedMessages": "1831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1832", "messages": "1833", "suppressedMessages": "1834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1835", "messages": "1836", "suppressedMessages": "1837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1838", "messages": "1839", "suppressedMessages": "1840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1841", "messages": "1842", "suppressedMessages": "1843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1844", "messages": "1845", "suppressedMessages": "1846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1847", "messages": "1848", "suppressedMessages": "1849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1850", "messages": "1851", "suppressedMessages": "1852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1853", "messages": "1854", "suppressedMessages": "1855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "1856", "messages": "1857", "suppressedMessages": "1858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1859", "messages": "1860", "suppressedMessages": "1861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1862", "messages": "1863", "suppressedMessages": "1864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1865", "messages": "1866", "suppressedMessages": "1867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1868", "messages": "1869", "suppressedMessages": "1870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1871", "messages": "1872", "suppressedMessages": "1873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1874", "messages": "1875", "suppressedMessages": "1876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1877", "messages": "1878", "suppressedMessages": "1879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1880", "messages": "1881", "suppressedMessages": "1882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1883", "messages": "1884", "suppressedMessages": "1885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1886", "messages": "1887", "suppressedMessages": "1888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1889", "messages": "1890", "suppressedMessages": "1891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1892", "messages": "1893", "suppressedMessages": "1894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1895", "messages": "1896", "suppressedMessages": "1897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1898", "messages": "1899", "suppressedMessages": "1900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1901", "messages": "1902", "suppressedMessages": "1903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1904", "messages": "1905", "suppressedMessages": "1906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1907", "messages": "1908", "suppressedMessages": "1909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1910", "messages": "1911", "suppressedMessages": "1912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1913", "messages": "1914", "suppressedMessages": "1915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1916", "messages": "1917", "suppressedMessages": "1918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1919", "messages": "1920", "suppressedMessages": "1921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1922", "messages": "1923", "suppressedMessages": "1924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1925", "messages": "1926", "suppressedMessages": "1927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1928", "messages": "1929", "suppressedMessages": "1930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1931", "messages": "1932", "suppressedMessages": "1933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1934", "messages": "1935", "suppressedMessages": "1936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1937", "messages": "1938", "suppressedMessages": "1939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1940", "messages": "1941", "suppressedMessages": "1942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1943", "messages": "1944", "suppressedMessages": "1945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1946", "messages": "1947", "suppressedMessages": "1948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1949", "messages": "1950", "suppressedMessages": "1951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1952", "messages": "1953", "suppressedMessages": "1954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1955", "messages": "1956", "suppressedMessages": "1957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "1958", "messages": "1959", "suppressedMessages": "1960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1961", "messages": "1962", "suppressedMessages": "1963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1964", "messages": "1965", "suppressedMessages": "1966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1967", "messages": "1968", "suppressedMessages": "1969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1970", "messages": "1971", "suppressedMessages": "1972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1973", "messages": "1974", "suppressedMessages": "1975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1976", "messages": "1977", "suppressedMessages": "1978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1979", "messages": "1980", "suppressedMessages": "1981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1982", "messages": "1983", "suppressedMessages": "1984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1985", "messages": "1986", "suppressedMessages": "1987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1988", "messages": "1989", "suppressedMessages": "1990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1991", "messages": "1992", "suppressedMessages": "1993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1994", "messages": "1995", "suppressedMessages": "1996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1997", "messages": "1998", "suppressedMessages": "1999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2000", "messages": "2001", "suppressedMessages": "2002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2003", "messages": "2004", "suppressedMessages": "2005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2006", "messages": "2007", "suppressedMessages": "2008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2009", "messages": "2010", "suppressedMessages": "2011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2012", "messages": "2013", "suppressedMessages": "2014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2015", "messages": "2016", "suppressedMessages": "2017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2018", "messages": "2019", "suppressedMessages": "2020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2021", "messages": "2022", "suppressedMessages": "2023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2024", "messages": "2025", "suppressedMessages": "2026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2027", "messages": "2028", "suppressedMessages": "2029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2030", "messages": "2031", "suppressedMessages": "2032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2033", "messages": "2034", "suppressedMessages": "2035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2036", "messages": "2037", "suppressedMessages": "2038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2039", "messages": "2040", "suppressedMessages": "2041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2042", "messages": "2043", "suppressedMessages": "2044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2045", "messages": "2046", "suppressedMessages": "2047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2048", "messages": "2049", "suppressedMessages": "2050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2051", "messages": "2052", "suppressedMessages": "2053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2054", "messages": "2055", "suppressedMessages": "2056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2057", "messages": "2058", "suppressedMessages": "2059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2060", "messages": "2061", "suppressedMessages": "2062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2063", "messages": "2064", "suppressedMessages": "2065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2066", "messages": "2067", "suppressedMessages": "2068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2069", "messages": "2070", "suppressedMessages": "2071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2072", "messages": "2073", "suppressedMessages": "2074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2075", "messages": "2076", "suppressedMessages": "2077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2078", "messages": "2079", "suppressedMessages": "2080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2081", "messages": "2082", "suppressedMessages": "2083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2084", "messages": "2085", "suppressedMessages": "2086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2087", "messages": "2088", "suppressedMessages": "2089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2090", "messages": "2091", "suppressedMessages": "2092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2093", "messages": "2094", "suppressedMessages": "2095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2096", "messages": "2097", "suppressedMessages": "2098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2099", "messages": "2100", "suppressedMessages": "2101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2102", "messages": "2103", "suppressedMessages": "2104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "2105", "messages": "2106", "suppressedMessages": "2107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2108", "messages": "2109", "suppressedMessages": "2110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2111", "messages": "2112", "suppressedMessages": "2113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2114", "messages": "2115", "suppressedMessages": "2116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2117", "messages": "2118", "suppressedMessages": "2119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2120", "messages": "2121", "suppressedMessages": "2122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2123", "messages": "2124", "suppressedMessages": "2125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2126", "messages": "2127", "suppressedMessages": "2128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2129", "messages": "2130", "suppressedMessages": "2131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2132", "messages": "2133", "suppressedMessages": "2134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2135", "messages": "2136", "suppressedMessages": "2137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2138", "messages": "2139", "suppressedMessages": "2140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2141", "messages": "2142", "suppressedMessages": "2143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2144", "messages": "2145", "suppressedMessages": "2146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2147", "messages": "2148", "suppressedMessages": "2149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2150", "messages": "2151", "suppressedMessages": "2152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2153", "messages": "2154", "suppressedMessages": "2155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2156", "messages": "2157", "suppressedMessages": "2158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2159", "messages": "2160", "suppressedMessages": "2161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 12, "source": null}, {"filePath": "2162", "messages": "2163", "suppressedMessages": "2164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2165", "messages": "2166", "suppressedMessages": "2167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2168", "messages": "2169", "suppressedMessages": "2170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2171", "messages": "2172", "suppressedMessages": "2173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2174", "messages": "2175", "suppressedMessages": "2176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2177", "messages": "2178", "suppressedMessages": "2179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2180", "messages": "2181", "suppressedMessages": "2182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2183", "messages": "2184", "suppressedMessages": "2185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2186", "messages": "2187", "suppressedMessages": "2188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2189", "messages": "2190", "suppressedMessages": "2191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2192", "messages": "2193", "suppressedMessages": "2194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2195", "messages": "2196", "suppressedMessages": "2197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2198", "messages": "2199", "suppressedMessages": "2200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2201", "messages": "2202", "suppressedMessages": "2203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2204", "messages": "2205", "suppressedMessages": "2206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2207", "messages": "2208", "suppressedMessages": "2209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2210", "messages": "2211", "suppressedMessages": "2212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2213", "messages": "2214", "suppressedMessages": "2215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2216", "messages": "2217", "suppressedMessages": "2218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2219", "messages": "2220", "suppressedMessages": "2221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2222", "messages": "2223", "suppressedMessages": "2224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2225", "messages": "2226", "suppressedMessages": "2227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2228", "messages": "2229", "suppressedMessages": "2230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2231", "messages": "2232", "suppressedMessages": "2233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2234", "messages": "2235", "suppressedMessages": "2236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2237", "messages": "2238", "suppressedMessages": "2239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2240", "messages": "2241", "suppressedMessages": "2242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2243", "messages": "2244", "suppressedMessages": "2245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2246", "messages": "2247", "suppressedMessages": "2248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2249", "messages": "2250", "suppressedMessages": "2251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2252", "messages": "2253", "suppressedMessages": "2254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2255", "messages": "2256", "suppressedMessages": "2257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2258", "messages": "2259", "suppressedMessages": "2260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2261", "messages": "2262", "suppressedMessages": "2263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2264", "messages": "2265", "suppressedMessages": "2266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2267", "messages": "2268", "suppressedMessages": "2269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2270", "messages": "2271", "suppressedMessages": "2272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2273", "messages": "2274", "suppressedMessages": "2275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2276", "messages": "2277", "suppressedMessages": "2278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2279", "messages": "2280", "suppressedMessages": "2281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2282", "messages": "2283", "suppressedMessages": "2284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2285", "messages": "2286", "suppressedMessages": "2287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2288", "messages": "2289", "suppressedMessages": "2290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2291", "messages": "2292", "suppressedMessages": "2293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2294", "messages": "2295", "suppressedMessages": "2296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 14, "source": null}, "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\admin\\health\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\admin\\security-stats\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\admin\\subscription-integrity\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\ai\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\analytics\\vitals\\route.ts", ["2297", "2298", "2299", "2300", "2301", "2302", "2303", "2304"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\api-docs\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\capture-oauth-error\\route.ts", [], ["2305", "2306"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\check-env\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-flow\\route.ts", [], ["2307", "2308", "2309"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-google\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-oauth\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug-providers\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\reset-rate-limit\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-config\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-google\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-login\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-providers\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth-callback\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\billing\\customer-portal\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\chat\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\checkout\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\checkout\\trial\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\csrf\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\db-status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\deprecated-usage\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\excel\\ai-process\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\excel\\download\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\excel\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\feedback\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\issues\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\repositories\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\workflows\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\ai\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\all\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\auth\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\database\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\db\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\debug\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\mcp\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\metrics\\route.ts", ["2310"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\stripe\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\init.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\legacy-redirect\\[...path]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\linear\\issues\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\linear\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\linear\\teams\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\metrics\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\migration-example\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\socket\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\customers\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\payments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\subscriptions\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\supabase\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\supabase\\storage\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\supabase\\tables\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\trpc\\[trpc]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\user\\api-usage\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\user\\subscription\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\vercel\\deployments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\vercel\\env\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\vercel\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\webhooks\\stripe\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbook\\save\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\recent\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\shared\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\collaborators\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\duplicate\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\export\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\sheets\\[sheetId]\\chunks\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\storage\\route.ts", [], ["2311", "2312", "2313", "2314", "2315", "2316"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\ws\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\auth\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\dashboard\\account\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\dashboard\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\dashboard\\page.tsx", ["2317"], ["2318"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\examples\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\pricing\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\pricing\\page.tsx", [], ["2319"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\privacy\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\providers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\templates\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\terms\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\new\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\[id]\\page.tsx", ["2320"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ai-status-indicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\AppInitializer.tsx", [], ["2321", "2322"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\billing\\customer-portal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\billing\\stripe-script-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chart-display-lazy.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chart-display.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\chat-interface.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\chat-message.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\ChatInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\CommandPalette.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\empty-state.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\message-content.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\operations-indicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\chat-interface\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\client-scripts.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ClientLayoutWrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ClientOnly.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\collaboration-indicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\collaboration-panel\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\collaboration-panel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\command-examples-wrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\command-examples.tsx", [], ["2323"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\command-feedback.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\command-preview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\command-selector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\create-workbook-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\dashboard\\QuickActions.tsx", ["2324", "2325", "2326", "2327", "2328", "2329", "2330", "2331", "2332", "2333", "2334", "2335", "2336", "2337", "2338", "2339", "2340", "2341", "2342", "2343", "2344", "2345", "2346", "2347", "2348", "2349", "2350", "2351", "2352", "2353", "2354", "2355", "2356", "2357", "2358", "2359", "2360", "2361", "2362", "2363", "2364", "2365", "2366", "2367", "2368", "2369"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\dashboard\\RecentActivity.tsx", ["2370", "2371", "2372", "2373", "2374", "2375", "2376", "2377", "2378", "2379", "2380", "2381", "2382", "2383", "2384", "2385", "2386", "2387", "2388", "2389", "2390", "2391", "2392", "2393", "2394", "2395", "2396", "2397", "2398", "2399", "2400", "2401", "2402", "2403", "2404", "2405", "2406", "2407", "2408", "2409", "2410", "2411", "2412", "2413", "2414", "2415", "2416", "2417", "2418", "2419", "2420", "2421", "2422", "2423"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\dashboard\\WorkbooksTable.tsx", ["2424", "2425"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\debug\\PerformanceDebugPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\enhanced-chat-input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\examples\\AnimationWrapperExample.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\examples\\MotionSafeExample.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\export-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\feature-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\icons\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ImageWithFallback.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\locale-switcher.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\mode-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\nav-bar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\providers\\csrf-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\realtime\\OnlineUsers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\rsc-error-suppressor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\settings-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\trpc-demo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\action-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\animation-wrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\card-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\empty-state.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\error-message.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\form-field-styles.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\loading-indicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\loading-optimization.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\motion-safe.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\motion.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\optimized-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\spinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\tab-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\theme-toggle\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\toaster.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\use-toast.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\virtual-table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\ui\\visually-hidden.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\upload-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\user-nav.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\user-onboarding\\ActiveTour.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\user-onboarding\\TourProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\user-onboarding\\TourStep.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\AIPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\components\\AIAssistantPanel.tsx", ["2426", "2427", "2428", "2429", "2430", "2431", "2432"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\components\\MobileChat.tsx", ["2433", "2434", "2435"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\components\\SpreadsheetGrid.tsx", ["2436", "2437", "2438", "2439"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\components\\SpreadsheetModals.tsx", ["2440", "2441", "2442", "2443", "2444", "2445", "2446", "2447", "2448", "2449", "2450", "2451", "2452", "2453", "2454", "2455"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\components\\SpreadsheetToolbar.tsx", ["2456", "2457", "2458"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\hooks\\useSpreadsheetData.ts", ["2459", "2460", "2461", "2462"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\hooks\\useSpreadsheetKeyboard.ts", ["2463"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\hooks\\useSpreadsheetUI.ts", ["2464", "2465", "2466", "2467", "2468", "2469", "2470", "2471", "2472"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\OptimizedTableComponents.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\QuickCommands.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\SpreadsheetContext.tsx", ["2473", "2474", "2475", "2476", "2477", "2478", "2479", "2480", "2481", "2482", "2483", "2484", "2485", "2486", "2487", "2488", "2489", "2490", "2491", "2492", "2493", "2494", "2495", "2496", "2497", "2498", "2499", "2500", "2501", "2502", "2503", "2504", "2505", "2506", "2507", "2508", "2509", "2510", "2511", "2512", "2513", "2514"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\SpreadsheetEditor.tsx", ["2515", "2516"], ["2517", "2518", "2519"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\SpreadsheetEditorRefactored.tsx", ["2520", "2521", "2522", "2523", "2524", "2525", "2526", "2527", "2528", "2529", "2530", "2531", "2532", "2533", "2534", "2535"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook\\SpreadsheetToolbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook-actions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook-list-item.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\workbook-templates.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\ai-prompts.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\auth-flags.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\i18n.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\mcp-config.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\security-validator.ts", [], ["2536"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\site.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\config\\unified-environment.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\context-providers\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\context-providers\\LocaleProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\context-providers\\ProviderComposer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\context-providers\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\contexts\\LocaleContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\use-media-query.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\use-media-query.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useAIChat.ts", [], ["2537"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useAnalytics.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useBidirectionalVirtualizer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useChunkedData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useChunkedSpreadsheet.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useCleanup.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useErrorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useExcelFile.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useExcelOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useExcelWorker.ts", ["2538"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useFileOperation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useI18n.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useKeyboardShortcut.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useReducedMotion.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useSocket.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useSSRSafeSession.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useSupabaseStorage.ts", [], ["2539"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useTranslation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\hooks\\useWorkbookRealtime.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\ai-adapter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\ai-factory.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\client-polyfill.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\client-safe.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\conditional-import.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\constants.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\dynamic-import.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\error-interceptor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\excel-desktop-connector.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\ExcelAIProcessor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\feedback-service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\gemini-api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\gemini-service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\mock-vertex-service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\prompts.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\provider.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\regex-test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\simple-error-suppressor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\ai\\webpack-interceptor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\animations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\api-tracker.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\api-usage.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\app-initializer.ts", ["2540"], ["2541"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth\\security.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth\\validation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth-audit-logger.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth-config.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth-monitoring.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\auth.ts", [], ["2542", "2543", "2544", "2545"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\cache\\ai-command-cache.ts", ["2546", "2547", "2548", "2549", "2550", "2551", "2552", "2553", "2554", "2555", "2556", "2557", "2558", "2559", "2560", "2561", "2562", "2563", "2564", "2565", "2566", "2567", "2568", "2569", "2570", "2571", "2572", "2573", "2574"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\cache\\cache-manager.ts", ["2575", "2576", "2577", "2578", "2579", "2580", "2581", "2582", "2583", "2584", "2585", "2586", "2587", "2588", "2589", "2590", "2591", "2592"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\cache\\redis-client.ts", ["2593"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\cache-manager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\chartOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\chunk-manager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\collaboration\\store.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\collaborative-sync.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\critical-css.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\design-tokens.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\edge-logger.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\env-validator.ts", [], ["2594", "2595", "2596", "2597", "2598", "2599", "2600", "2601", "2602", "2603", "2604", "2605", "2606", "2607", "2608", "2609", "2610", "2611", "2612", "2613", "2614", "2615", "2616", "2617", "2618", "2619", "2620", "2621", "2622", "2623", "2624", "2625", "2626", "2627", "2628", "2629"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\error-handler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\error-reporting.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\errors.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\executionOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\fileOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\operationUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\parserOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\testUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel\\typeUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\excel.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\fallback-handlers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\github-integration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checker.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\ai.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\index.ts", ["2630", "2631"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\mcp.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks\\stripe.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\health-checks.ts", [], ["2632"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\linear-integration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\logger.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\mcp-tools.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\memory-monitor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\oauth-limiter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\payment-limiter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\plan-based-rate-limiter-secure.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\plan-based-rate-limiter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\rate-limiter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\withErrorHandling.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\middleware\\withServerValidation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\mock-db.ts", [], ["2633", "2634", "2635", "2636", "2637", "2638", "2639"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\advancedChartOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\advancedVisualizationOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\cellOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\chartOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\columnOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\conditionalFormattingOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\dataTransformations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\filterOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\formatOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\formulaOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\pivotTableOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\processor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\tableOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\operations\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\patches.js", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\performance-monitor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\prisma.ts", ["2640", "2641", "2642"], ["2643"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\rate-limiter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\rate-limiting\\oauth-rate-limiter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\csrf-protection.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\edge-csrf.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\enhanced-rate-limiter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\pattern-detection.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\rate-limiter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\sanitization-excel.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\sanitization.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security\\sanitize-html.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\security-monitor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\services.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\session-helpers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\stripe-integration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\stripe.ts", [], ["2644", "2645"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\subscription-limits.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\supabase\\realtime.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\supabase\\storage.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\supabase-integration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\trpc.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\utils\\empty-module.js", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\utils\\error-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\utils\\rate-limit.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\utils\\request.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\validators\\workbook.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\lib\\vercel-integration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\middleware\\auth-callback-fix.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\middleware\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\middleware\\core.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\middleware\\metrics.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\middleware\\rate-limit.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\providers\\toast-wrapper.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\schemas\\socket.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\schemas\\workbook.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\ai\\base-ai-service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\ai\\gemini-service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\ai\\vertex-ai-service.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\api\\root.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\api-usage.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\auth\\options.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\db\\client.ts", [], ["2646"], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\db\\edge-client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\db\\query-cache.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\db\\universal-client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\db\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\init.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\services\\workbook-service.ts", ["2647", "2648", "2649", "2650", "2651"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\test-utils\\mock-server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\trpc\\init-procedure.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\trpc\\react.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\trpc\\router.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\trpc\\trpc.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\websocket.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\server\\workbook.actions.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\ai-processing.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\ai.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\analytics.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\api.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\bridge.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\chart-extensions.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\collaboration.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\component-extensions.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\component-fixes.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\component-props.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\custom.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\dashboard.ts", ["2652", "2653", "2654", "2655", "2656", "2657", "2658", "2659", "2660", "2661", "2662", "2663"], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\errors.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\events.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\excel-unified.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\form-event-extensions.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\form-events.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\global-types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\global.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\gtag.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\interfaces.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\jest.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\next-auth.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\optional-types.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\optional-types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\prisma-extensions.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\regex-types.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\regex-types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\service-extensions.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\shared.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\sheet.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\socket.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\test-types.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\three-environment.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\trpc.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\ui-components.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\ui-components.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\types\\vertex-ai.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\api-response.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\data\\json.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\data-access.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\error-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\excel-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\functions\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\http\\api-response.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\json.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\logger-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\optional-helpers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\performance-monitor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\regex-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\route-migration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\safe-access.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\type-helpers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\utils\\usage-example.ts", [], [], "C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\workers\\excel-operations.worker.ts", ["2664", "2665", "2666", "2667", "2668", "2669", "2670", "2671", "2672", "2673", "2674", "2675", "2676", "2677", "2678", "2679"], [], {"ruleId": "2680", "severity": 1, "message": "2681", "line": 1, "column": 1, "nodeType": "2682", "endLine": 1, "endColumn": 57, "fix": "2683"}, {"ruleId": "2680", "severity": 1, "message": "2684", "line": 2, "column": 1, "nodeType": "2682", "endLine": 2, "endColumn": 39}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 6, "column": 38, "nodeType": "2687", "messageId": "2688", "endLine": 6, "endColumn": 41, "suggestions": "2689"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 7, "column": 36, "nodeType": "2687", "messageId": "2688", "endLine": 7, "endColumn": 39, "suggestions": "2690"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 7, "column": 52, "nodeType": "2687", "messageId": "2688", "endLine": 7, "endColumn": 55, "suggestions": "2691"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 8, "column": 38, "nodeType": "2687", "messageId": "2688", "endLine": 8, "endColumn": 41, "suggestions": "2692"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 9, "column": 59, "nodeType": "2687", "messageId": "2688", "endLine": 9, "endColumn": 62, "suggestions": "2693"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 10, "column": 73, "nodeType": null, "messageId": "2696", "endLine": 10, "endColumn": 73, "fix": "2697"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 56, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 56, "endColumn": 16, "suggestions": "2702", "suppressions": "2703"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 151, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 151, "endColumn": 16, "suggestions": "2704", "suppressions": "2705"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 22, "column": 62, "nodeType": "2687", "messageId": "2688", "endLine": 22, "endColumn": 65, "suggestions": "2706", "suppressions": "2707"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 28, "column": 36, "nodeType": "2687", "messageId": "2688", "endLine": 28, "endColumn": 39, "suggestions": "2708", "suppressions": "2709"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 174, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 174, "endColumn": 16, "suggestions": "2710", "suppressions": "2711"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 18, "column": 68, "nodeType": null, "messageId": "2696", "endLine": 18, "endColumn": 68, "fix": "2712"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 15, "column": 60, "nodeType": "2687", "messageId": "2688", "endLine": 15, "endColumn": 63, "suggestions": "2713", "suppressions": "2714"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 15, "column": 69, "nodeType": "2687", "messageId": "2688", "endLine": 15, "endColumn": 72, "suggestions": "2715", "suppressions": "2716"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 68, "column": 60, "nodeType": "2687", "messageId": "2688", "endLine": 68, "endColumn": 63, "suggestions": "2717", "suppressions": "2718"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 68, "column": 69, "nodeType": "2687", "messageId": "2688", "endLine": 68, "endColumn": 72, "suggestions": "2719", "suppressions": "2720"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 176, "column": 60, "nodeType": "2687", "messageId": "2688", "endLine": 176, "endColumn": 63, "suggestions": "2721", "suppressions": "2722"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 176, "column": 69, "nodeType": "2687", "messageId": "2688", "endLine": 176, "endColumn": 72, "suggestions": "2723", "suppressions": "2724"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 111, "column": 23, "nodeType": null, "messageId": "2696", "endLine": 111, "endColumn": 23, "fix": "2725"}, {"ruleId": "2726", "severity": 1, "message": "2727", "line": 157, "column": 41, "nodeType": "2728", "endLine": 157, "endColumn": 48, "suppressions": "2729"}, {"ruleId": "2726", "severity": 1, "message": "2730", "line": 164, "column": 6, "nodeType": "2731", "endLine": 164, "endColumn": 39, "suggestions": "2732", "suppressions": "2733"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 74, "column": 15, "nodeType": null, "messageId": "2696", "endLine": 74, "endColumn": 15, "fix": "2734"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 166, "column": 13, "nodeType": "2700", "messageId": "2701", "endLine": 166, "endColumn": 24, "suggestions": "2735", "suppressions": "2736"}, {"ruleId": "2726", "severity": 1, "message": "2737", "line": 222, "column": 6, "nodeType": "2731", "endLine": 222, "endColumn": 8, "suggestions": "2738", "suppressions": "2739"}, {"ruleId": "2726", "severity": 1, "message": "2740", "line": 183, "column": 5, "nodeType": "2731", "endLine": 183, "endColumn": 15, "suggestions": "2741", "suppressions": "2742"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 12, "column": 12, "nodeType": null, "messageId": "2696", "endLine": 12, "endColumn": 12, "fix": "2743"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 47, "column": 31, "nodeType": null, "messageId": "2745", "endLine": 47, "endColumn": 32, "fix": "2746"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 48, "column": 13, "nodeType": null, "messageId": "2745", "endLine": 48, "endColumn": 14, "fix": "2747"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 51, "column": 23, "nodeType": null, "messageId": "2696", "endLine": 51, "endColumn": 23, "fix": "2748"}, {"ruleId": "2694", "severity": 1, "message": "2749", "line": 87, "column": 30, "nodeType": null, "messageId": "2750", "endLine": 87, "endColumn": 33, "fix": "2751"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 146, "column": 27, "nodeType": null, "messageId": "2696", "endLine": 146, "endColumn": 27, "fix": "2752"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 154, "column": 18, "nodeType": null, "messageId": "2696", "endLine": 154, "endColumn": 18, "fix": "2753"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 163, "column": 28, "nodeType": null, "messageId": "2696", "endLine": 163, "endColumn": 28, "fix": "2754"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 172, "column": 45, "nodeType": null, "messageId": "2696", "endLine": 172, "endColumn": 45, "fix": "2755"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 173, "column": 6, "nodeType": null, "messageId": "2696", "endLine": 173, "endColumn": 6, "fix": "2756"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 184, "column": 27, "nodeType": null, "messageId": "2696", "endLine": 184, "endColumn": 27, "fix": "2757"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 192, "column": 27, "nodeType": null, "messageId": "2696", "endLine": 192, "endColumn": 27, "fix": "2758"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 193, "column": 6, "nodeType": null, "messageId": "2696", "endLine": 193, "endColumn": 6, "fix": "2759"}, {"ruleId": "2694", "severity": 1, "message": "2760", "line": 197, "column": 25, "nodeType": null, "messageId": "2750", "endLine": 197, "endColumn": 27, "fix": "2761"}, {"ruleId": "2694", "severity": 1, "message": "2762", "line": 200, "column": 26, "nodeType": null, "messageId": "2750", "endLine": 202, "endColumn": 9, "fix": "2763"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 204, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 204, "endColumn": 7, "fix": "2765"}, {"ruleId": "2694", "severity": 1, "message": "2766", "line": 208, "column": 31, "nodeType": null, "messageId": "2750", "endLine": 208, "endColumn": 39, "fix": "2767"}, {"ruleId": "2694", "severity": 1, "message": "2768", "line": 213, "column": 17, "nodeType": null, "messageId": "2750", "endLine": 213, "endColumn": 74, "fix": "2769"}, {"ruleId": "2694", "severity": 1, "message": "2770", "line": 214, "column": 36, "nodeType": null, "messageId": "2750", "endLine": 214, "endColumn": 67, "fix": "2771"}, {"ruleId": "2694", "severity": 1, "message": "2772", "line": 230, "column": 59, "nodeType": null, "messageId": "2750", "endLine": 232, "endColumn": 15, "fix": "2773"}, {"ruleId": "2694", "severity": 1, "message": "2766", "line": 239, "column": 33, "nodeType": null, "messageId": "2750", "endLine": 239, "endColumn": 41, "fix": "2774"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 269, "column": 30, "nodeType": null, "messageId": "2696", "endLine": 269, "endColumn": 30, "fix": "2775"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 275, "column": 29, "nodeType": null, "messageId": "2696", "endLine": 275, "endColumn": 29, "fix": "2776"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 281, "column": 31, "nodeType": null, "messageId": "2696", "endLine": 281, "endColumn": 31, "fix": "2777"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 282, "column": 6, "nodeType": null, "messageId": "2696", "endLine": 282, "endColumn": 6, "fix": "2778"}, {"ruleId": "2694", "severity": 1, "message": "2760", "line": 291, "column": 25, "nodeType": null, "messageId": "2750", "endLine": 291, "endColumn": 27, "fix": "2779"}, {"ruleId": "2694", "severity": 1, "message": "2780", "line": 294, "column": 26, "nodeType": null, "messageId": "2750", "endLine": 296, "endColumn": 9, "fix": "2781"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 298, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 298, "endColumn": 7, "fix": "2782"}, {"ruleId": "2694", "severity": 1, "message": "2783", "line": 301, "column": 26, "nodeType": null, "messageId": "2750", "endLine": 301, "endColumn": 36, "fix": "2784"}, {"ruleId": "2694", "severity": 1, "message": "2785", "line": 308, "column": 34, "nodeType": null, "messageId": "2750", "endLine": 310, "endColumn": 15, "fix": "2786"}, {"ruleId": "2694", "severity": 1, "message": "2787", "line": 315, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 315, "endColumn": 9, "fix": "2788"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 316, "column": 16, "nodeType": null, "messageId": "2745", "endLine": 316, "endColumn": 17, "fix": "2789"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 317, "column": 28, "nodeType": null, "messageId": "2745", "endLine": 317, "endColumn": 29, "fix": "2790"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 318, "column": 20, "nodeType": null, "messageId": "2745", "endLine": 318, "endColumn": 21, "fix": "2791"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 332, "column": 30, "nodeType": null, "messageId": "2745", "endLine": 332, "endColumn": 31, "fix": "2792"}, {"ruleId": "2694", "severity": 1, "message": "2793", "line": 334, "column": 12, "nodeType": null, "messageId": "2750", "endLine": 334, "endColumn": 13, "fix": "2794"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 335, "column": 5, "nodeType": null, "messageId": "2745", "endLine": 335, "endColumn": 6, "fix": "2795"}, {"ruleId": "2694", "severity": 1, "message": "2760", "line": 354, "column": 25, "nodeType": null, "messageId": "2750", "endLine": 354, "endColumn": 27, "fix": "2796"}, {"ruleId": "2694", "severity": 1, "message": "2797", "line": 357, "column": 26, "nodeType": null, "messageId": "2750", "endLine": 359, "endColumn": 9, "fix": "2798"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 361, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 361, "endColumn": 7, "fix": "2799"}, {"ruleId": "2694", "severity": 1, "message": "2800", "line": 364, "column": 44, "nodeType": null, "messageId": "2750", "endLine": 364, "endColumn": 54, "fix": "2801"}, {"ruleId": "2694", "severity": 1, "message": "2802", "line": 373, "column": 61, "nodeType": null, "messageId": "2750", "endLine": 375, "endColumn": 17, "fix": "2803"}, {"ruleId": "2694", "severity": 1, "message": "2787", "line": 383, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 383, "endColumn": 9, "fix": "2804"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 385, "column": 18, "nodeType": null, "messageId": "2745", "endLine": 385, "endColumn": 19, "fix": "2805"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 386, "column": 30, "nodeType": null, "messageId": "2745", "endLine": 386, "endColumn": 31, "fix": "2806"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 387, "column": 22, "nodeType": null, "messageId": "2745", "endLine": 387, "endColumn": 23, "fix": "2807"}, {"ruleId": "2680", "severity": 1, "message": "2808", "line": 3, "column": 1, "nodeType": "2682", "endLine": 11, "endColumn": 23, "fix": "2809"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 3, "column": 9, "nodeType": null, "messageId": "2745", "endLine": 3, "endColumn": 10, "fix": "2810"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 4, "column": 19, "nodeType": null, "messageId": "2745", "endLine": 4, "endColumn": 20, "fix": "2811"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 5, "column": 9, "nodeType": null, "messageId": "2745", "endLine": 5, "endColumn": 10, "fix": "2812"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 6, "column": 9, "nodeType": null, "messageId": "2745", "endLine": 6, "endColumn": 10, "fix": "2813"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 7, "column": 12, "nodeType": null, "messageId": "2745", "endLine": 7, "endColumn": 13, "fix": "2814"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 10, "column": 17, "nodeType": null, "messageId": "2696", "endLine": 10, "endColumn": 17, "fix": "2815"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 53, "column": 21, "nodeType": null, "messageId": "2696", "endLine": 53, "endColumn": 21, "fix": "2816"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 59, "column": 20, "nodeType": null, "messageId": "2696", "endLine": 59, "endColumn": 20, "fix": "2817"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 65, "column": 16, "nodeType": null, "messageId": "2696", "endLine": 65, "endColumn": 16, "fix": "2818"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 71, "column": 25, "nodeType": null, "messageId": "2696", "endLine": 71, "endColumn": 25, "fix": "2819"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 72, "column": 4, "nodeType": null, "messageId": "2696", "endLine": 72, "endColumn": 4, "fix": "2820"}, {"ruleId": "2694", "severity": 1, "message": "2821", "line": 81, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 81, "endColumn": 3, "fix": "2822"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 85, "column": 17, "nodeType": null, "messageId": "2696", "endLine": 85, "endColumn": 17, "fix": "2823"}, {"ruleId": "2694", "severity": 1, "message": "2824", "line": 95, "column": 11, "nodeType": null, "messageId": "2696", "endLine": 95, "endColumn": 11, "fix": "2825"}, {"ruleId": "2694", "severity": 1, "message": "2826", "line": 96, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 96, "endColumn": 78, "fix": "2827"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 97, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 97, "endColumn": 1, "fix": "2829"}, {"ruleId": "2694", "severity": 1, "message": "2830", "line": 98, "column": 7, "nodeType": null, "messageId": "2750", "endLine": 98, "endColumn": 9, "fix": "2831"}, {"ruleId": "2694", "severity": 1, "message": "2832", "line": 99, "column": 29, "nodeType": null, "messageId": "2750", "endLine": 99, "endColumn": 38, "fix": "2833"}, {"ruleId": "2694", "severity": 1, "message": "2834", "line": 106, "column": 64, "nodeType": null, "messageId": "2750", "endLine": 108, "endColumn": 13, "fix": "2835"}, {"ruleId": "2694", "severity": 1, "message": "2836", "line": 118, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 118, "endColumn": 11, "fix": "2837"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 122, "column": 22, "nodeType": null, "messageId": "2745", "endLine": 122, "endColumn": 23, "fix": "2838"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 123, "column": 32, "nodeType": null, "messageId": "2745", "endLine": 123, "endColumn": 33, "fix": "2839"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 124, "column": 26, "nodeType": null, "messageId": "2745", "endLine": 124, "endColumn": 27, "fix": "2840"}, {"ruleId": "2694", "severity": 1, "message": "2841", "line": 139, "column": 33, "nodeType": null, "messageId": "2750", "endLine": 141, "endColumn": 15, "fix": "2842"}, {"ruleId": "2694", "severity": 1, "message": "2843", "line": 146, "column": 68, "nodeType": null, "messageId": "2750", "endLine": 148, "endColumn": 9, "fix": "2844"}, {"ruleId": "2694", "severity": 1, "message": "2845", "line": 157, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 157, "endColumn": 13, "fix": "2846"}, {"ruleId": "2694", "severity": 1, "message": "2847", "line": 158, "column": 44, "nodeType": null, "messageId": "2750", "endLine": 160, "endColumn": 14, "fix": "2848"}, {"ruleId": "2694", "severity": 1, "message": "2845", "line": 161, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 161, "endColumn": 13, "fix": "2849"}, {"ruleId": "2694", "severity": 1, "message": "2850", "line": 162, "column": 46, "nodeType": null, "messageId": "2750", "endLine": 164, "endColumn": 14, "fix": "2851"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 194, "column": 33, "nodeType": null, "messageId": "2745", "endLine": 194, "endColumn": 34, "fix": "2852"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 195, "column": 14, "nodeType": null, "messageId": "2745", "endLine": 195, "endColumn": 15, "fix": "2853"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 196, "column": 13, "nodeType": null, "messageId": "2745", "endLine": 196, "endColumn": 14, "fix": "2854"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 197, "column": 13, "nodeType": null, "messageId": "2745", "endLine": 197, "endColumn": 14, "fix": "2855"}, {"ruleId": "2694", "severity": 1, "message": "2793", "line": 198, "column": 16, "nodeType": null, "messageId": "2750", "endLine": 198, "endColumn": 17, "fix": "2856"}, {"ruleId": "2694", "severity": 1, "message": "2760", "line": 203, "column": 25, "nodeType": null, "messageId": "2750", "endLine": 203, "endColumn": 27, "fix": "2857"}, {"ruleId": "2694", "severity": 1, "message": "2858", "line": 208, "column": 30, "nodeType": null, "messageId": "2750", "endLine": 210, "endColumn": 13, "fix": "2859"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 219, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 219, "endColumn": 7, "fix": "2860"}, {"ruleId": "2694", "severity": 1, "message": "2861", "line": 229, "column": 36, "nodeType": null, "messageId": "2750", "endLine": 229, "endColumn": 46, "fix": "2862"}, {"ruleId": "2694", "severity": 1, "message": "2863", "line": 236, "column": 70, "nodeType": null, "messageId": "2750", "endLine": 238, "endColumn": 13, "fix": "2864"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 252, "column": 40, "nodeType": null, "messageId": "2745", "endLine": 252, "endColumn": 41, "fix": "2865"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 253, "column": 14, "nodeType": null, "messageId": "2745", "endLine": 253, "endColumn": 15, "fix": "2866"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 254, "column": 13, "nodeType": null, "messageId": "2745", "endLine": 254, "endColumn": 14, "fix": "2867"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 255, "column": 13, "nodeType": null, "messageId": "2745", "endLine": 255, "endColumn": 14, "fix": "2868"}, {"ruleId": "2694", "severity": 1, "message": "2793", "line": 256, "column": 15, "nodeType": null, "messageId": "2750", "endLine": 256, "endColumn": 16, "fix": "2869"}, {"ruleId": "2694", "severity": 1, "message": "2870", "line": 262, "column": 26, "nodeType": null, "messageId": "2750", "endLine": 262, "endColumn": 37, "fix": "2871"}, {"ruleId": "2694", "severity": 1, "message": "2870", "line": 277, "column": 24, "nodeType": null, "messageId": "2750", "endLine": 277, "endColumn": 35, "fix": "2872"}, {"ruleId": "2694", "severity": 1, "message": "2861", "line": 278, "column": 30, "nodeType": null, "messageId": "2750", "endLine": 278, "endColumn": 40, "fix": "2873"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 283, "column": 23, "nodeType": null, "messageId": "2696", "endLine": 283, "endColumn": 23, "fix": "2874"}, {"ruleId": "2694", "severity": 1, "message": "2875", "line": 288, "column": 17, "nodeType": null, "messageId": "2696", "endLine": 288, "endColumn": 17, "fix": "2876"}, {"ruleId": "2694", "severity": 1, "message": "2877", "line": 289, "column": 15, "nodeType": null, "messageId": "2750", "endLine": 289, "endColumn": 84, "fix": "2878"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 290, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 290, "endColumn": 1, "fix": "2879"}, {"ruleId": "2694", "severity": 1, "message": "2880", "line": 291, "column": 13, "nodeType": null, "messageId": "2750", "endLine": 291, "endColumn": 15, "fix": "2881"}, {"ruleId": "2694", "severity": 1, "message": "2882", "line": 292, "column": 35, "nodeType": null, "messageId": "2750", "endLine": 292, "endColumn": 44, "fix": "2883"}, {"ruleId": "2726", "severity": 1, "message": "2884", "line": 212, "column": 6, "nodeType": "2731", "endLine": 212, "endColumn": 53, "suggestions": "2885"}, {"ruleId": "2726", "severity": 1, "message": "2886", "line": 229, "column": 6, "nodeType": "2731", "endLine": 229, "endColumn": 25, "suggestions": "2887"}, {"ruleId": "2680", "severity": 1, "message": "2888", "line": 4, "column": 1, "nodeType": "2682", "endLine": 4, "endColumn": 66, "fix": "2889"}, {"ruleId": "2680", "severity": 1, "message": "2890", "line": 8, "column": 1, "nodeType": "2682", "endLine": 8, "endColumn": 65, "fix": "2891"}, {"ruleId": "2680", "severity": 1, "message": "2892", "line": 9, "column": 1, "nodeType": "2682", "endLine": 9, "endColumn": 61, "fix": "2893"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 117, "column": 15, "nodeType": null, "messageId": "2696", "endLine": 117, "endColumn": 15, "fix": "2894"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 118, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 118, "endColumn": 1, "fix": "2895"}, {"ruleId": "2896", "severity": 2, "message": "2897", "line": 138, "column": 64, "nodeType": "2898", "messageId": "2899", "suggestions": "2900"}, {"ruleId": "2896", "severity": 2, "message": "2897", "line": 138, "column": 70, "nodeType": "2898", "messageId": "2899", "suggestions": "2901"}, {"ruleId": "2680", "severity": 1, "message": "2888", "line": 4, "column": 1, "nodeType": "2682", "endLine": 4, "endColumn": 41, "fix": "2902"}, {"ruleId": "2680", "severity": 1, "message": "2892", "line": 8, "column": 1, "nodeType": "2682", "endLine": 8, "endColumn": 61, "fix": "2903"}, {"ruleId": "2694", "severity": 1, "message": "2904", "line": 53, "column": 28, "nodeType": null, "messageId": "2750", "endLine": 57, "endColumn": 15, "fix": "2905"}, {"ruleId": "2680", "severity": 1, "message": "2906", "line": 4, "column": 1, "nodeType": "2682", "endLine": 4, "endColumn": 58, "fix": "2907"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 42, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 42, "endColumn": 7, "fix": "2908"}, {"ruleId": "2694", "severity": 1, "message": "2909", "line": 77, "column": 26, "nodeType": null, "messageId": "2750", "endLine": 79, "endColumn": 6, "fix": "2910"}, {"ruleId": "2694", "severity": 1, "message": "2911", "line": 109, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 109, "endColumn": 15, "fix": "2912"}, {"ruleId": "2680", "severity": 1, "message": "2888", "line": 4, "column": 1, "nodeType": "2682", "endLine": 4, "endColumn": 69, "fix": "2913"}, {"ruleId": "2680", "severity": 1, "message": "2914", "line": 14, "column": 1, "nodeType": "2682", "endLine": 14, "endColumn": 98, "fix": "2915"}, {"ruleId": "2680", "severity": 1, "message": "2916", "line": 15, "column": 1, "nodeType": "2682", "endLine": 15, "endColumn": 47, "fix": "2917"}, {"ruleId": "2694", "severity": 1, "message": "2918", "line": 60, "column": 16, "nodeType": null, "messageId": "2696", "endLine": 60, "endColumn": 16, "fix": "2919"}, {"ruleId": "2694", "severity": 1, "message": "2875", "line": 103, "column": 99, "nodeType": null, "messageId": "2696", "endLine": 103, "endColumn": 99, "fix": "2920"}, {"ruleId": "2694", "severity": 1, "message": "2921", "line": 180, "column": 26, "nodeType": null, "messageId": "2750", "endLine": 182, "endColumn": 13, "fix": "2922"}, {"ruleId": "2694", "severity": 1, "message": "2923", "line": 190, "column": 95, "nodeType": null, "messageId": "2745", "endLine": 190, "endColumn": 104, "fix": "2924"}, {"ruleId": "2694", "severity": 1, "message": "2925", "line": 191, "column": 19, "nodeType": null, "messageId": "2696", "endLine": 191, "endColumn": 19, "fix": "2926"}, {"ruleId": "2896", "severity": 2, "message": "2897", "line": 200, "column": 65, "nodeType": "2898", "messageId": "2899", "suggestions": "2927"}, {"ruleId": "2896", "severity": 2, "message": "2897", "line": 200, "column": 91, "nodeType": "2898", "messageId": "2899", "suggestions": "2928"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 200, "column": 95, "nodeType": null, "messageId": "2745", "endLine": 200, "endColumn": 96, "fix": "2929"}, {"ruleId": "2896", "severity": 2, "message": "2897", "line": 201, "column": 19, "nodeType": "2898", "messageId": "2899", "suggestions": "2930"}, {"ruleId": "2896", "severity": 2, "message": "2897", "line": 201, "column": 47, "nodeType": "2898", "messageId": "2899", "suggestions": "2931"}, {"ruleId": "2694", "severity": 1, "message": "2932", "line": 210, "column": 88, "nodeType": null, "messageId": "2750", "endLine": 211, "endColumn": 31, "fix": "2933"}, {"ruleId": "2694", "severity": 1, "message": "2934", "line": 220, "column": 98, "nodeType": null, "messageId": "2696", "endLine": 220, "endColumn": 98, "fix": "2935"}, {"ruleId": "2694", "severity": 1, "message": "2936", "line": 221, "column": 18, "nodeType": null, "messageId": "2745", "endLine": 221, "endColumn": 21, "fix": "2937"}, {"ruleId": "2680", "severity": 1, "message": "2888", "line": 4, "column": 1, "nodeType": "2682", "endLine": 15, "endColumn": 23, "fix": "2938"}, {"ruleId": "2694", "severity": 1, "message": "2939", "line": 157, "column": 61, "nodeType": null, "messageId": "2750", "endLine": 157, "endColumn": 111, "fix": "2940"}, {"ruleId": "2694", "severity": 1, "message": "2941", "line": 211, "column": 22, "nodeType": null, "messageId": "2750", "endLine": 216, "endColumn": 15, "fix": "2942"}, {"ruleId": "2694", "severity": 1, "message": "2943", "line": 19, "column": 37, "nodeType": null, "messageId": "2750", "endLine": 19, "endColumn": 73, "fix": "2944"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 107, "column": 18, "nodeType": "2687", "messageId": "2688", "endLine": 107, "endColumn": 21, "suggestions": "2945"}, {"ruleId": "2694", "severity": 1, "message": "2946", "line": 122, "column": 13, "nodeType": null, "messageId": "2750", "endLine": 126, "endColumn": 9, "fix": "2947"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 144, "column": 42, "nodeType": "2687", "messageId": "2688", "endLine": 144, "endColumn": 45, "suggestions": "2948"}, {"ruleId": "2949", "severity": 2, "message": "2950", "line": 81, "column": 9, "nodeType": "2951", "messageId": "2701", "endLine": 89, "endColumn": 17}, {"ruleId": "2680", "severity": 1, "message": "2952", "line": 2, "column": 1, "nodeType": "2682", "endLine": 2, "endColumn": 45, "fix": "2953"}, {"ruleId": "2694", "severity": 1, "message": "2954", "line": 97, "column": 45, "nodeType": null, "messageId": "2696", "endLine": 97, "endColumn": 45, "fix": "2955"}, {"ruleId": "2694", "severity": 1, "message": "2956", "line": 98, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 98, "endColumn": 5, "fix": "2957"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 99, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 99, "endColumn": 1, "fix": "2958"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 100, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 100, "endColumn": 7, "fix": "2959"}, {"ruleId": "2694", "severity": 1, "message": "2956", "line": 101, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 101, "endColumn": 5, "fix": "2960"}, {"ruleId": "2694", "severity": 1, "message": "2961", "line": 102, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 102, "endColumn": 15, "fix": "2962"}, {"ruleId": "2694", "severity": 1, "message": "2963", "line": 200, "column": 28, "nodeType": null, "messageId": "2750", "endLine": 200, "endColumn": 45, "fix": "2964"}, {"ruleId": "2694", "severity": 1, "message": "2744", "line": 201, "column": 55, "nodeType": null, "messageId": "2745", "endLine": 201, "endColumn": 56, "fix": "2965"}, {"ruleId": "2694", "severity": 1, "message": "2966", "line": 227, "column": 34, "nodeType": null, "messageId": "2696", "endLine": 227, "endColumn": 34, "fix": "2967"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 228, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 228, "endColumn": 1, "fix": "2968"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 229, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 229, "endColumn": 7, "fix": "2969"}, {"ruleId": "2694", "severity": 1, "message": "2970", "line": 231, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 231, "endColumn": 7, "fix": "2971"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 232, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 232, "endColumn": 1, "fix": "2972"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 234, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 234, "endColumn": 7, "fix": "2973"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 235, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 235, "endColumn": 1, "fix": "2974"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 236, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 236, "endColumn": 7, "fix": "2975"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 238, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 238, "endColumn": 7, "fix": "2976"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 239, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 239, "endColumn": 1, "fix": "2977"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 240, "column": 9, "nodeType": null, "messageId": "2696", "endLine": 240, "endColumn": 9, "fix": "2978"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 241, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 241, "endColumn": 1, "fix": "2979"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 242, "column": 9, "nodeType": null, "messageId": "2696", "endLine": 242, "endColumn": 9, "fix": "2980"}, {"ruleId": "2694", "severity": 1, "message": "2970", "line": 243, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 243, "endColumn": 7, "fix": "2981"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 245, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 245, "endColumn": 1, "fix": "2982"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 246, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 246, "endColumn": 7, "fix": "2983"}, {"ruleId": "2694", "severity": 1, "message": "2984", "line": 247, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 247, "endColumn": 9, "fix": "2985"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 248, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 248, "endColumn": 1, "fix": "2986"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 250, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 250, "endColumn": 7, "fix": "2987"}, {"ruleId": "2694", "severity": 1, "message": "2970", "line": 251, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 251, "endColumn": 7, "fix": "2988"}, {"ruleId": "2694", "severity": 1, "message": "2989", "line": 252, "column": 5, "nodeType": null, "messageId": "2750", "endLine": 252, "endColumn": 30, "fix": "2990"}, {"ruleId": "2694", "severity": 1, "message": "2824", "line": 259, "column": 25, "nodeType": null, "messageId": "2696", "endLine": 259, "endColumn": 25, "fix": "2991"}, {"ruleId": "2694", "severity": 1, "message": "2966", "line": 292, "column": 28, "nodeType": null, "messageId": "2696", "endLine": 292, "endColumn": 28, "fix": "2992"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 293, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 293, "endColumn": 7, "fix": "2993"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 295, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 295, "endColumn": 1, "fix": "2994"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 297, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 297, "endColumn": 7, "fix": "2995"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 298, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 298, "endColumn": 1, "fix": "2996"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 300, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 300, "endColumn": 7, "fix": "2997"}, {"ruleId": "2694", "severity": 1, "message": "2989", "line": 301, "column": 5, "nodeType": null, "messageId": "2750", "endLine": 301, "endColumn": 30, "fix": "2998"}, {"ruleId": "2694", "severity": 1, "message": "2966", "line": 303, "column": 31, "nodeType": null, "messageId": "2696", "endLine": 303, "endColumn": 31, "fix": "2999"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 304, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 304, "endColumn": 1, "fix": "3000"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 306, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 306, "endColumn": 1, "fix": "3001"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 308, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 308, "endColumn": 7, "fix": "3002"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 309, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 309, "endColumn": 1, "fix": "3003"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 311, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 311, "endColumn": 7, "fix": "3004"}, {"ruleId": "2694", "severity": 1, "message": "2984", "line": 312, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 312, "endColumn": 9, "fix": "3005"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 313, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 313, "endColumn": 1, "fix": "3006"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 314, "column": 9, "nodeType": null, "messageId": "2696", "endLine": 314, "endColumn": 9, "fix": "3007"}, {"ruleId": "2694", "severity": 1, "message": "2970", "line": 315, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 315, "endColumn": 7, "fix": "3008"}, {"ruleId": "2694", "severity": 1, "message": "3009", "line": 317, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 317, "endColumn": 98, "fix": "3010"}, {"ruleId": "2694", "severity": 1, "message": "2989", "line": 318, "column": 5, "nodeType": null, "messageId": "2750", "endLine": 318, "endColumn": 30, "fix": "3011"}, {"ruleId": "2694", "severity": 1, "message": "3012", "line": 327, "column": 10, "nodeType": null, "messageId": "2750", "endLine": 331, "endColumn": 4, "fix": "3013"}, {"ruleId": "2726", "severity": 1, "message": "3014", "line": 367, "column": 5, "nodeType": "2731", "endLine": 367, "endColumn": 57, "suggestions": "3015"}, {"ruleId": "2726", "severity": 1, "message": "3016", "line": 800, "column": 6, "nodeType": "2731", "endLine": 800, "endColumn": 21, "suggestions": "3017"}, {"ruleId": "2726", "severity": 1, "message": "3018", "line": 836, "column": 6, "nodeType": "2731", "endLine": 836, "endColumn": 19, "suggestions": "3019", "suppressions": "3020"}, {"ruleId": "2726", "severity": 1, "message": "3021", "line": 917, "column": 5, "nodeType": "2731", "endLine": 917, "endColumn": 74, "suggestions": "3022", "suppressions": "3023"}, {"ruleId": "2726", "severity": 1, "message": "3024", "line": 1157, "column": 6, "nodeType": "2731", "endLine": 1157, "endColumn": 33, "suggestions": "3025", "suppressions": "3026"}, {"ruleId": "2680", "severity": 1, "message": "3027", "line": 3, "column": 1, "nodeType": "2682", "endLine": 3, "endColumn": 55, "fix": "3028"}, {"ruleId": "2680", "severity": 1, "message": "3029", "line": 10, "column": 1, "nodeType": "2682", "endLine": 10, "endColumn": 77, "fix": "3030"}, {"ruleId": "2680", "severity": 1, "message": "3031", "line": 11, "column": 1, "nodeType": "2682", "endLine": 11, "endColumn": 65, "fix": "3032"}, {"ruleId": "2680", "severity": 1, "message": "3033", "line": 12, "column": 1, "nodeType": "2682", "endLine": 12, "endColumn": 61, "fix": "3034"}, {"ruleId": "2680", "severity": 1, "message": "3035", "line": 13, "column": 1, "nodeType": "2682", "endLine": 13, "endColumn": 73, "fix": "3036"}, {"ruleId": "2680", "severity": 1, "message": "3037", "line": 14, "column": 1, "nodeType": "2682", "endLine": 14, "endColumn": 70, "fix": "3038"}, {"ruleId": "2680", "severity": 1, "message": "3039", "line": 15, "column": 1, "nodeType": "2682", "endLine": 15, "endColumn": 64, "fix": "3040"}, {"ruleId": "2694", "severity": 1, "message": "2918", "line": 76, "column": 78, "nodeType": null, "messageId": "2696", "endLine": 76, "endColumn": 78, "fix": "3041"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 77, "column": 5, "nodeType": null, "messageId": "2696", "endLine": 77, "endColumn": 5, "fix": "3042"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 78, "column": 5, "nodeType": null, "messageId": "2696", "endLine": 78, "endColumn": 5, "fix": "3043"}, {"ruleId": "2694", "severity": 1, "message": "2956", "line": 79, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 79, "endColumn": 5, "fix": "3044"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 80, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 80, "endColumn": 1, "fix": "3045"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 81, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 81, "endColumn": 1, "fix": "3046"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 82, "column": 5, "nodeType": null, "messageId": "2696", "endLine": 82, "endColumn": 5, "fix": "3047"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 83, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 83, "endColumn": 1, "fix": "3048"}, {"ruleId": "2694", "severity": 1, "message": "3049", "line": 235, "column": 25, "nodeType": null, "messageId": "2750", "endLine": 239, "endColumn": 5, "fix": "3050"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 298, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 298, "endColumn": 16, "suggestions": "3051", "suppressions": "3052"}, {"ruleId": "2726", "severity": 1, "message": "3053", "line": 444, "column": 5, "nodeType": "2731", "endLine": 456, "endColumn": 6, "suggestions": "3054", "suppressions": "3055"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 8, "column": 58, "nodeType": null, "messageId": "2696", "endLine": 8, "endColumn": 58, "fix": "3056"}, {"ruleId": "2726", "severity": 1, "message": "3057", "line": 83, "column": 5, "nodeType": "2731", "endLine": 83, "endColumn": 7, "suggestions": "3058", "suppressions": "3059"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 22, "column": 24, "nodeType": null, "messageId": "2696", "endLine": 22, "endColumn": 24, "fix": "3060"}, {"ruleId": "3061", "severity": 2, "message": "3062", "line": 31, "column": 3, "nodeType": "3063", "messageId": "3064", "endLine": 31, "endColumn": 70, "suppressions": "3065"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 11, "column": 48, "nodeType": "2687", "messageId": "2688", "endLine": 11, "endColumn": 51, "suggestions": "3066", "suppressions": "3067"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 20, "column": 24, "nodeType": "2687", "messageId": "2688", "endLine": 20, "endColumn": 27, "suggestions": "3068", "suppressions": "3069"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 29, "column": 22, "nodeType": "2687", "messageId": "2688", "endLine": 29, "endColumn": 25, "suggestions": "3070", "suppressions": "3071"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 38, "column": 22, "nodeType": "2687", "messageId": "2688", "endLine": 38, "endColumn": 25, "suggestions": "3072", "suppressions": "3073"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 12, "column": 11, "nodeType": "2687", "messageId": "2688", "endLine": 12, "endColumn": 14, "suggestions": "3074"}, {"ruleId": "2694", "severity": 1, "message": "3075", "line": 41, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 41, "endColumn": 5, "fix": "3076"}, {"ruleId": "2694", "severity": 1, "message": "3077", "line": 42, "column": 18, "nodeType": null, "messageId": "2750", "endLine": 46, "endColumn": 7, "fix": "3078"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 64, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 64, "endColumn": 7, "fix": "3079"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 66, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 66, "endColumn": 7, "fix": "3080"}, {"ruleId": "2694", "severity": 1, "message": "2787", "line": 73, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 73, "endColumn": 9, "fix": "3081"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 76, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 76, "endColumn": 7, "fix": "3082"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 82, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 82, "endColumn": 7, "fix": "3083"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 98, "column": 13, "nodeType": "2687", "messageId": "2688", "endLine": 98, "endColumn": 16, "suggestions": "3084"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 109, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 109, "endColumn": 7, "fix": "3085"}, {"ruleId": "2694", "severity": 1, "message": "3086", "line": 115, "column": 33, "nodeType": null, "messageId": "2696", "endLine": 115, "endColumn": 33, "fix": "3087"}, {"ruleId": "2694", "severity": 1, "message": "3088", "line": 116, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 116, "endColumn": 1, "fix": "3089"}, {"ruleId": "2694", "severity": 1, "message": "3090", "line": 117, "column": 1, "nodeType": null, "messageId": "2750", "endLine": 117, "endColumn": 11, "fix": "3091"}, {"ruleId": "2694", "severity": 1, "message": "3088", "line": 118, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 118, "endColumn": 1, "fix": "3092"}, {"ruleId": "2694", "severity": 1, "message": "3093", "line": 119, "column": 9, "nodeType": null, "messageId": "2750", "endLine": 119, "endColumn": 10, "fix": "3094"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 121, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 121, "endColumn": 7, "fix": "3095"}, {"ruleId": "2694", "severity": 1, "message": "3096", "line": 122, "column": 46, "nodeType": null, "messageId": "2750", "endLine": 126, "endColumn": 7, "fix": "3097"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 127, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 127, "endColumn": 7, "fix": "3098"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 136, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 136, "endColumn": 7, "fix": "3099"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 154, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 154, "endColumn": 7, "fix": "3100"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 159, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 159, "endColumn": 7, "fix": "3101"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 173, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 173, "endColumn": 7, "fix": "3102"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 178, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 178, "endColumn": 7, "fix": "3103"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 187, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 187, "endColumn": 7, "fix": "3104"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 194, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 194, "endColumn": 7, "fix": "3105"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 212, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 212, "endColumn": 7, "fix": "3106"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 218, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 218, "endColumn": 7, "fix": "3107"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 237, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 237, "endColumn": 7, "fix": "3108"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 262, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 262, "endColumn": 7, "fix": "3109"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 61, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 61, "endColumn": 7, "fix": "3110"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 83, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 83, "endColumn": 7, "fix": "3111"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 89, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 89, "endColumn": 7, "fix": "3112"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 104, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 104, "endColumn": 7, "fix": "3113"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 108, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 108, "endColumn": 7, "fix": "3114"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 147, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 147, "endColumn": 7, "fix": "3115"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 150, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 150, "endColumn": 7, "fix": "3116"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 165, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 165, "endColumn": 7, "fix": "3117"}, {"ruleId": "2694", "severity": 1, "message": "3118", "line": 187, "column": 23, "nodeType": null, "messageId": "2750", "endLine": 189, "endColumn": 5, "fix": "3119"}, {"ruleId": "2694", "severity": 1, "message": "3120", "line": 198, "column": 23, "nodeType": null, "messageId": "2750", "endLine": 201, "endColumn": 5, "fix": "3121"}, {"ruleId": "2694", "severity": 1, "message": "3118", "line": 203, "column": 23, "nodeType": null, "messageId": "2750", "endLine": 205, "endColumn": 5, "fix": "3122"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 216, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 216, "endColumn": 7, "fix": "3123"}, {"ruleId": "2694", "severity": 1, "message": "3124", "line": 224, "column": 22, "nodeType": null, "messageId": "2750", "endLine": 228, "endColumn": 7, "fix": "3125"}, {"ruleId": "2694", "severity": 1, "message": "2764", "line": 242, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 242, "endColumn": 7, "fix": "3126"}, {"ruleId": "2694", "severity": 1, "message": "3127", "line": 251, "column": 22, "nodeType": null, "messageId": "2750", "endLine": 255, "endColumn": 7, "fix": "3128"}, {"ruleId": "2694", "severity": 1, "message": "3075", "line": 268, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 268, "endColumn": 5, "fix": "3129"}, {"ruleId": "2694", "severity": 1, "message": "3130", "line": 269, "column": 24, "nodeType": null, "messageId": "2750", "endLine": 272, "endColumn": 5, "fix": "3131"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 306, "column": 37, "nodeType": "2687", "messageId": "2688", "endLine": 306, "endColumn": 40, "suggestions": "3132"}, {"ruleId": "2694", "severity": 1, "message": "3133", "line": 67, "column": 43, "nodeType": null, "messageId": "2750", "endLine": 67, "endColumn": 74, "fix": "3134"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 538, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 538, "endColumn": 14, "suggestions": "3135", "suppressions": "3136"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 539, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 539, "endColumn": 14, "suggestions": "3137", "suppressions": "3138"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 540, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 540, "endColumn": 14, "suggestions": "3139", "suppressions": "3140"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 543, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 543, "endColumn": 14, "suggestions": "3141", "suppressions": "3142"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 546, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 546, "endColumn": 14, "suggestions": "3143", "suppressions": "3144"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 547, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 547, "endColumn": 14, "suggestions": "3145", "suppressions": "3146"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 548, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 548, "endColumn": 14, "suggestions": "3147", "suppressions": "3148"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 549, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 549, "endColumn": 14, "suggestions": "3149", "suppressions": "3150"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 563, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 563, "endColumn": 16, "suggestions": "3151", "suppressions": "3152"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 569, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 569, "endColumn": 18, "suggestions": "3153", "suppressions": "3154"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 576, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 576, "endColumn": 18, "suggestions": "3155", "suppressions": "3156"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 577, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 577, "endColumn": 18, "suggestions": "3157", "suppressions": "3158"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 578, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 578, "endColumn": 18, "suggestions": "3159", "suppressions": "3160"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 586, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 586, "endColumn": 18, "suggestions": "3161", "suppressions": "3162"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 587, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 587, "endColumn": 18, "suggestions": "3163", "suppressions": "3164"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 588, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 588, "endColumn": 18, "suggestions": "3165", "suppressions": "3166"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 589, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 589, "endColumn": 18, "suggestions": "3167", "suppressions": "3168"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 594, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 594, "endColumn": 18, "suggestions": "3169", "suppressions": "3170"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 595, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 595, "endColumn": 18, "suggestions": "3171", "suppressions": "3172"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 601, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 601, "endColumn": 16, "suggestions": "3173", "suppressions": "3174"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 612, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 612, "endColumn": 14, "suggestions": "3175", "suppressions": "3176"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 613, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 613, "endColumn": 14, "suggestions": "3177", "suppressions": "3178"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 614, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 614, "endColumn": 14, "suggestions": "3179", "suppressions": "3180"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 619, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 619, "endColumn": 16, "suggestions": "3181", "suppressions": "3182"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 623, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 623, "endColumn": 18, "suggestions": "3183", "suppressions": "3184"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 624, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 624, "endColumn": 18, "suggestions": "3185", "suppressions": "3186"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 625, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 625, "endColumn": 18, "suggestions": "3187", "suppressions": "3188"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 633, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 633, "endColumn": 18, "suggestions": "3189", "suppressions": "3190"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 636, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 636, "endColumn": 18, "suggestions": "3191", "suppressions": "3192"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 637, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 637, "endColumn": 18, "suggestions": "3193", "suppressions": "3194"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 638, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 638, "endColumn": 18, "suggestions": "3195", "suppressions": "3196"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 646, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 646, "endColumn": 14, "suggestions": "3197", "suppressions": "3198"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 647, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 647, "endColumn": 14, "suggestions": "3199", "suppressions": "3200"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 650, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 650, "endColumn": 14, "suggestions": "3201", "suppressions": "3202"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 651, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 651, "endColumn": 14, "suggestions": "3203", "suppressions": "3204"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 652, "column": 3, "nodeType": "2700", "messageId": "2701", "endLine": 652, "endColumn": 14, "suggestions": "3205", "suppressions": "3206"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 25, "column": 45, "nodeType": null, "messageId": "2696", "endLine": 25, "endColumn": 45, "fix": "3207"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 30, "column": 69, "nodeType": null, "messageId": "2696", "endLine": 30, "endColumn": 69, "fix": "3208"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 351, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 351, "endColumn": 18, "suggestions": "3209", "suppressions": "3210"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 109, "column": 7, "nodeType": "2700", "messageId": "2701", "endLine": 109, "endColumn": 18, "suggestions": "3211", "suppressions": "3212"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 121, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 121, "endColumn": 16, "suggestions": "3213", "suppressions": "3214"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 126, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 126, "endColumn": 16, "suggestions": "3215", "suppressions": "3216"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 131, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 131, "endColumn": 16, "suggestions": "3217", "suppressions": "3218"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 137, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 137, "endColumn": 16, "suggestions": "3219", "suppressions": "3220"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 159, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 159, "endColumn": 16, "suggestions": "3221", "suppressions": "3222"}, {"ruleId": "3223", "severity": 2, "message": "3224", "line": 167, "column": 24, "nodeType": "3225", "messageId": "3226", "endLine": 167, "endColumn": 53, "suppressions": "3227"}, {"ruleId": "2694", "severity": 1, "message": "3228", "line": 9, "column": 15, "nodeType": null, "messageId": "2750", "endLine": 9, "endColumn": 38, "fix": "3229"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 10, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 10, "endColumn": 1, "fix": "3230"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 11, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 11, "endColumn": 1, "fix": "3231"}, {"ruleId": "3061", "severity": 2, "message": "3062", "line": 5, "column": 3, "nodeType": "3063", "messageId": "3064", "endLine": 5, "endColumn": 42, "suppressions": "3232"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 166, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 166, "endColumn": 16, "suggestions": "3233", "suppressions": "3234"}, {"ruleId": "2698", "severity": 1, "message": "2699", "line": 168, "column": 5, "nodeType": "2700", "messageId": "2701", "endLine": 168, "endColumn": 16, "suggestions": "3235", "suppressions": "3236"}, {"ruleId": "3061", "severity": 2, "message": "3062", "line": 27, "column": 3, "nodeType": "3063", "messageId": "3064", "endLine": 27, "endColumn": 40, "suppressions": "3237"}, {"ruleId": "2680", "severity": 1, "message": "2684", "line": 2, "column": 1, "nodeType": "2682", "endLine": 2, "endColumn": 39}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 9, "column": 63, "nodeType": null, "messageId": "2696", "endLine": 9, "endColumn": 63, "fix": "3238"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 14, "column": 48, "nodeType": null, "messageId": "2696", "endLine": 14, "endColumn": 48, "fix": "3239"}, {"ruleId": "2694", "severity": 1, "message": "3240", "line": 361, "column": 63, "nodeType": null, "messageId": "2750", "endLine": 365, "endColumn": 13, "fix": "3241"}, {"ruleId": "2694", "severity": 1, "message": "3242", "line": 371, "column": 16, "nodeType": null, "messageId": "2745", "endLine": 372, "endColumn": 1, "fix": "3243"}, {"ruleId": "2694", "severity": 1, "message": "3244", "line": 80, "column": 8, "nodeType": null, "messageId": "2750", "endLine": 80, "endColumn": 122, "fix": "3245"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 201, "column": 12, "nodeType": null, "messageId": "2696", "endLine": 201, "endColumn": 12, "fix": "3246"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 208, "column": 17, "nodeType": null, "messageId": "2696", "endLine": 208, "endColumn": 17, "fix": "3247"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 215, "column": 33, "nodeType": null, "messageId": "2696", "endLine": 215, "endColumn": 33, "fix": "3248"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 225, "column": 18, "nodeType": null, "messageId": "2696", "endLine": 225, "endColumn": 18, "fix": "3249"}, {"ruleId": "2694", "severity": 1, "message": "3250", "line": 231, "column": 39, "nodeType": null, "messageId": "2750", "endLine": 231, "endColumn": 74, "fix": "3251"}, {"ruleId": "2694", "severity": 1, "message": "3252", "line": 235, "column": 10, "nodeType": null, "messageId": "2750", "endLine": 235, "endColumn": 17, "fix": "3253"}, {"ruleId": "2694", "severity": 1, "message": "3254", "line": 239, "column": 38, "nodeType": null, "messageId": "2696", "endLine": 239, "endColumn": 38, "fix": "3255"}, {"ruleId": "2694", "severity": 1, "message": "3256", "line": 243, "column": 10, "nodeType": null, "messageId": "2696", "endLine": 243, "endColumn": 10, "fix": "3257"}, {"ruleId": "2694", "severity": 1, "message": "3254", "line": 248, "column": 34, "nodeType": null, "messageId": "2696", "endLine": 248, "endColumn": 34, "fix": "3258"}, {"ruleId": "2694", "severity": 1, "message": "3256", "line": 252, "column": 10, "nodeType": null, "messageId": "2696", "endLine": 252, "endColumn": 10, "fix": "3259"}, {"ruleId": "2694", "severity": 1, "message": "3254", "line": 256, "column": 43, "nodeType": null, "messageId": "2696", "endLine": 256, "endColumn": 43, "fix": "3260"}, {"ruleId": "2680", "severity": 1, "message": "3261", "line": 11, "column": 1, "nodeType": "2682", "endLine": 11, "endColumn": 54, "fix": "3262"}, {"ruleId": "2680", "severity": 1, "message": "3263", "line": 12, "column": 1, "nodeType": "2682", "endLine": 12, "endColumn": 40, "fix": "3264"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 17, "column": 37, "nodeType": "2687", "messageId": "2688", "endLine": 17, "endColumn": 40, "suggestions": "3265"}, {"ruleId": "2685", "severity": 1, "message": "2686", "line": 20, "column": 36, "nodeType": "2687", "messageId": "2688", "endLine": 20, "endColumn": 39, "suggestions": "3266"}, {"ruleId": "2694", "severity": 1, "message": "2695", "line": 23, "column": 4, "nodeType": null, "messageId": "2696", "endLine": 23, "endColumn": 4, "fix": "3267"}, {"ruleId": "2694", "severity": 1, "message": "3268", "line": 58, "column": 13, "nodeType": null, "messageId": "2750", "endLine": 58, "endColumn": 111, "fix": "3269"}, {"ruleId": "2694", "severity": 1, "message": "3242", "line": 163, "column": 1, "nodeType": null, "messageId": "2745", "endLine": 164, "endColumn": 1, "fix": "3270"}, {"ruleId": "2694", "severity": 1, "message": "3271", "line": 191, "column": 16, "nodeType": null, "messageId": "2750", "endLine": 191, "endColumn": 23, "fix": "3272"}, {"ruleId": "2694", "severity": 1, "message": "2918", "line": 195, "column": 23, "nodeType": null, "messageId": "2696", "endLine": 195, "endColumn": 23, "fix": "3273"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 196, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 196, "endColumn": 1, "fix": "3274"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 197, "column": 5, "nodeType": null, "messageId": "2696", "endLine": 197, "endColumn": 5, "fix": "3275"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 198, "column": 7, "nodeType": null, "messageId": "2696", "endLine": 198, "endColumn": 7, "fix": "3276"}, {"ruleId": "2694", "severity": 1, "message": "2828", "line": 199, "column": 1, "nodeType": null, "messageId": "2696", "endLine": 199, "endColumn": 1, "fix": "3277"}, {"ruleId": "2694", "severity": 1, "message": "3278", "line": 212, "column": 29, "nodeType": null, "messageId": "2750", "endLine": 212, "endColumn": 36, "fix": "3279"}, {"ruleId": "2694", "severity": 1, "message": "3280", "line": 228, "column": 27, "nodeType": null, "messageId": "2750", "endLine": 235, "endColumn": 5, "fix": "3281"}, {"ruleId": "2694", "severity": 1, "message": "3282", "line": 237, "column": 2, "nodeType": null, "messageId": "2696", "endLine": 237, "endColumn": 2, "fix": "3283"}, "import/order", "There should be at least one empty line between import groups", "ImportDeclaration", {"range": "3284", "text": "3285"}, "There should be no empty line within import group", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["3286", "3287"], ["3288", "3289"], ["3290", "3291"], ["3292", "3293"], ["3294", "3295"], "prettier/prettier", "Insert `,`", "insert", {"range": "3296", "text": "3297"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["3298"], ["3299"], ["3300"], ["3301"], ["3302", "3303"], ["3304"], ["3305", "3306"], ["3307"], ["3308"], ["3309"], {"range": "3310", "text": "3297"}, ["3311", "3312"], ["3313"], ["3314", "3315"], ["3316"], ["3317", "3318"], ["3319"], ["3320", "3321"], ["3322"], ["3323", "3324"], ["3325"], ["3326", "3327"], ["3328"], {"range": "3329", "text": "3297"}, "react-hooks/exhaustive-deps", "The ref value 'abortControllersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'abortControllersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", ["3330"], "React Hook useEffect has missing dependencies: 'handlePlanSelect' and 'handleTrialSelect'. Either include them or remove the dependency array.", "ArrayExpression", ["3331"], ["3332"], {"range": "3333", "text": "3297"}, ["3334"], ["3335"], "React Hook useEffect has missing dependencies: 'initPhase' and 'isLoading'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setProgress' needs the current value of 'initPhase'.", ["3336"], ["3337"], "React Hook useCallback has a missing dependency: 'commandCategories'. Either include it or remove the dependency array.", ["3338"], ["3339"], {"range": "3340", "text": "3297"}, "Delete `·`", "delete", {"range": "3341", "text": "3342"}, {"range": "3343", "text": "3342"}, {"range": "3344", "text": "3297"}, "Replace `(e)` with `e`", "replace", {"range": "3345", "text": "3346"}, {"range": "3347", "text": "3297"}, {"range": "3348", "text": "3297"}, {"range": "3349", "text": "3297"}, {"range": "3350", "text": "3297"}, {"range": "3351", "text": "3297"}, {"range": "3352", "text": "3297"}, {"range": "3353", "text": "3297"}, {"range": "3354", "text": "3297"}, "Replace `\"\"` with `''`", {"range": "3355", "text": "3356"}, "Replace `␍⏎··········Comece·rapidamente·com·suas·planilhas␍⏎········` with `Comece·rapidamente·com·suas·planilhas`", {"range": "3357", "text": "3358"}, "Delete `······`", {"range": "3359", "text": "3342"}, "Replace `(action)` with `action`", {"range": "3360", "text": "3361"}, "Replace `\"h-auto·p-4·flex·flex-col·items-start·space-y-2·relative\"` with `'h-auto·p-4·flex·flex-col·items-start·space-y-2·relative'`", {"range": "3362", "text": "3363"}, "Replace `\"opacity-50·cursor-not-allowed\"` with `'opacity-50·cursor-not-allowed'`", {"range": "3364", "text": "3365"}, "Replace `␍⏎················{action.description}␍⏎··············` with `{action.description}`", {"range": "3366", "text": "3367"}, {"range": "3368", "text": "3361"}, {"range": "3369", "text": "3297"}, {"range": "3370", "text": "3297"}, {"range": "3371", "text": "3297"}, {"range": "3372", "text": "3297"}, {"range": "3373", "text": "3356"}, "Replace `␍⏎··········Comece·com·modelos·prontos␍⏎········` with `Comece·com·modelos·prontos`", {"range": "3374", "text": "3375"}, {"range": "3376", "text": "3342"}, "Replace `(template)` with `template`", {"range": "3377", "text": "3378"}, "Replace `\"mr-3\",·template.color)}>␍⏎················{template.icon}␍⏎··············` with `'mr-3',·template.color)}>{template.icon}`", {"range": "3379", "text": "3380"}, "Delete `········`", {"range": "3381", "text": "3342"}, {"range": "3382", "text": "3342"}, {"range": "3383", "text": "3342"}, {"range": "3384", "text": "3342"}, {"range": "3385", "text": "3342"}, "Replace `·` with `,`", {"range": "3386", "text": "3297"}, {"range": "3387", "text": "3342"}, {"range": "3388", "text": "3356"}, "Replace `␍⏎··········Suas·planilhas·mais·recentes␍⏎········` with `Suas·planilhas·mais·recentes`", {"range": "3389", "text": "3390"}, {"range": "3391", "text": "3342"}, "Replace `(workbook)` with `workbook`", {"range": "3392", "text": "3393"}, "Replace `␍⏎··················{workbook.name}␍⏎················` with `{workbook.name}`", {"range": "3394", "text": "3395"}, {"range": "3396", "text": "3342"}, {"range": "3397", "text": "3342"}, {"range": "3398", "text": "3342"}, {"range": "3399", "text": "3342"}, "`lucide-react` import should occur after import of `date-fns/locale`", {"range": "3400", "text": "3401"}, {"range": "3402", "text": "3342"}, {"range": "3403", "text": "3342"}, {"range": "3404", "text": "3342"}, {"range": "3405", "text": "3342"}, {"range": "3406", "text": "3342"}, {"range": "3407", "text": "3297"}, {"range": "3408", "text": "3297"}, {"range": "3409", "text": "3297"}, {"range": "3410", "text": "3297"}, {"range": "3411", "text": "3297"}, {"range": "3412", "text": "3297"}, "Delete `··`", {"range": "3413", "text": "3342"}, {"range": "3414", "text": "3297"}, "Insert `␍⏎·······`", {"range": "3415", "text": "3416"}, "Replace `········\"flex-shrink-0·w-8·h-8·rounded-full·flex·items-center·justify-center\"` with `··········'flex-shrink-0·w-8·h-8·rounded-full·flex·items-center·justify-center'`", {"range": "3417", "text": "3418"}, "Insert `··`", {"range": "3419", "text": "3420"}, "Replace `)}` with `··)}␍⏎······`", {"range": "3421", "text": "3422"}, "Replace `\"h-4·w-4\"` with `'h-4·w-4'`", {"range": "3423", "text": "3424"}, "Replace `␍⏎··············{activity.title}␍⏎············` with `{activity.title}`", {"range": "3425", "text": "3426"}, "Delete `··········`", {"range": "3427", "text": "3342"}, {"range": "3428", "text": "3342"}, {"range": "3429", "text": "3342"}, {"range": "3430", "text": "3342"}, "Replace `␍⏎················Ver·Detalhes␍⏎··············` with `Ver·Detalhes`", {"range": "3431", "text": "3432"}, "Replace `␍⏎··········{activity.description}␍⏎········` with `{activity.description}`", {"range": "3433", "text": "3434"}, "Delete `············`", {"range": "3435", "text": "3342"}, "Replace `(␍⏎··············<span>por·{activity.metadata.sharedBy}</span>␍⏎············)` with `<span>por·{activity.metadata.sharedBy}</span>`", {"range": "3436", "text": "3437"}, {"range": "3438", "text": "3342"}, "Replace `(␍⏎··············<span>com·{activity.metadata.sharedWith}</span>␍⏎············)` with `<span>com·{activity.metadata.sharedWith}</span>`", {"range": "3439", "text": "3440"}, {"range": "3441", "text": "3342"}, {"range": "3442", "text": "3342"}, {"range": "3443", "text": "3342"}, {"range": "3444", "text": "3342"}, {"range": "3445", "text": "3297"}, {"range": "3446", "text": "3356"}, "Replace `␍⏎··············Suas·últimas·ações·no·Excel·Copilot␍⏎············` with `Suas·últimas·ações·no·Excel·Copilot`", {"range": "3447", "text": "3448"}, {"range": "3449", "text": "3342"}, "Replace `(activity)` with `activity`", {"range": "3450", "text": "3451"}, "Replace `␍⏎··············Nenhuma·atividade·recente␍⏎············` with `Nenhuma·atividade·recente`", {"range": "3452", "text": "3453"}, {"range": "3454", "text": "3342"}, {"range": "3455", "text": "3342"}, {"range": "3456", "text": "3342"}, {"range": "3457", "text": "3342"}, {"range": "3458", "text": "3297"}, "Replace `\"space-y-2\"` with `'space-y-2'`", {"range": "3459", "text": "3460"}, {"range": "3461", "text": "3460"}, {"range": "3462", "text": "3451"}, {"range": "3463", "text": "3297"}, "Insert `␍⏎·············`", {"range": "3464", "text": "3465"}, "Replace `\"flex-shrink-0·w-6·h-6·rounded-full·flex·items-center·justify-center\"` with `··'flex-shrink-0·w-6·h-6·rounded-full·flex·items-center·justify-center'`", {"range": "3466", "text": "3467"}, {"range": "3468", "text": "3420"}, "Replace `)}` with `··)}␍⏎············`", {"range": "3469", "text": "3470"}, "Replace `\"h-3·w-3\"` with `'h-3·w-3'`", {"range": "3471", "text": "3472"}, "React Hook useCallback has missing dependencies: 'loadError', 'session?.user', and 'workbooks.length'. Either include them or remove the dependency array.", ["3473"], "React Hook useEffect has a missing dependency: 'fetchWorkbooks'. Either include it or remove the dependency array.", ["3474"], "`lucide-react` import should occur before import of `react`", {"range": "3475", "text": "3476"}, "`@/components/ui/alert` import should occur before import of `@/components/ui/button`", {"range": "3477", "text": "3478"}, "`@/components/chat-interface` import should occur before import of `@/components/ui/button`", {"range": "3479", "text": "3480"}, {"range": "3481", "text": "3420"}, {"range": "3482", "text": "3420"}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["3483", "3484", "3485", "3486"], ["3487", "3488", "3489", "3490"], {"range": "3491", "text": "3492"}, {"range": "3493", "text": "3494"}, "Replace `␍⏎················readOnly␍⏎··················?·'Modo·somente·leitura'␍⏎··················:·'Digite·um·comando·para·a·IA...'␍⏎··············` with `readOnly·?·'Modo·somente·leitura'·:·'Digite·um·comando·para·a·IA...'`", {"range": "3495", "text": "3496"}, "`@tanstack/react-virtual` import should occur before import of `react`", {"range": "3497", "text": "3498"}, {"range": "3499", "text": "3342"}, "Replace `␍⏎······lastModifiedCell.row·===·rowIndex·&&␍⏎·····` with `·lastModifiedCell.row·===·rowIndex·&&`", {"range": "3500", "text": "3501"}, "Delete `··············`", {"range": "3502", "text": "3342"}, {"range": "3503", "text": "3504"}, "`@/components/ui/card` import should occur before import of `@/components/ui/dialog`", {"range": "3505", "text": "3506"}, "`@/components/ui/badge` import should occur before import of `@/components/ui/button`", {"range": "3507", "text": "3508"}, "Insert `␍⏎···`", {"range": "3509", "text": "3510"}, {"range": "3511", "text": "3465"}, "Replace `␍⏎··············Bem-vindo·ao·Excel·Copilot!·({tutorialStep·+·1}/4)␍⏎············` with `Bem-vindo·ao·Excel·Copilot!·({tutorialStep·+·1}/4)`", {"range": "3512", "text": "3513"}, "Delete `·natural.`", {"range": "3514", "text": "3342"}, "Insert `natural.·`", {"range": "3515", "text": "3516"}, ["3517", "3518", "3519", "3520"], ["3521", "3522", "3523", "3524"], {"range": "3525", "text": "3342"}, ["3526", "3527", "3528", "3529"], ["3530", "3531", "3532", "3533"], "Replace `␍⏎··················e·?·para·ver` with `·e·?·para·ver␍⏎·················`", {"range": "3534", "text": "3535"}, "Insert `·ou`", {"range": "3536", "text": "3537"}, "Delete `·ou`", {"range": "3538", "text": "3342"}, {"range": "3539", "text": "3540"}, "Replace `·component:·'SpreadsheetToolbar',·action:·'charts'` with `␍⏎····················component:·'SpreadsheetToolbar',␍⏎····················action:·'charts',␍⏎·················`", {"range": "3541", "text": "3542"}, "Replace `␍⏎················variant=\"ghost\"␍⏎················size=\"sm\"␍⏎················onClick={onToggleAIPanel}␍⏎················className=\"h-8·px-3\"␍⏎··············` with `·variant=\"ghost\"·size=\"sm\"·onClick={onToggleAIPanel}·className=\"h-8·px-3\"`", {"range": "3543", "text": "3544"}, "Replace `·workbookId,·onSave,·initialCommand·` with `␍⏎··workbookId,␍⏎··onSave,␍⏎··initialCommand,␍⏎`", {"range": "3545", "text": "3546"}, ["3547", "3548"], "Replace `␍⏎··········op.type·===·'cell_update'·&&␍⏎··········typeof·op.row·===·'number'·&&␍⏎··········typeof·op.col·===·'number'␍⏎········` with `op.type·===·'cell_update'·&&·typeof·op.row·===·'number'·&&·typeof·op.col·===·'number'`", {"range": "3549", "text": "3550"}, ["3551", "3552"], "no-duplicate-case", "Duplicate case label.", "SwitchCase", "`next/navigation` import should occur before import of `react`", {"range": "3553", "text": "3554"}, "Insert `␍⏎····`", {"range": "3555", "text": "3556"}, "Replace `····` with `······`", {"range": "3557", "text": "3558"}, {"range": "3559", "text": "3420"}, {"range": "3560", "text": "3420"}, {"range": "3561", "text": "3558"}, "Replace `··},·[actions]` with `····},␍⏎····[actions]␍⏎··`", {"range": "3562", "text": "3563"}, "Replace `·apiUsageInfo·&&·` with `␍⏎······apiUsageInfo·&&`", {"range": "3564", "text": "3565"}, {"range": "3566", "text": "3342"}, "Insert `␍⏎······`", {"range": "3567", "text": "3568"}, {"range": "3569", "text": "3420"}, {"range": "3570", "text": "3420"}, "Replace `······` with `········`", {"range": "3571", "text": "3572"}, {"range": "3573", "text": "3420"}, {"range": "3574", "text": "3420"}, {"range": "3575", "text": "3420"}, {"range": "3576", "text": "3420"}, {"range": "3577", "text": "3420"}, {"range": "3578", "text": "3420"}, {"range": "3579", "text": "3420"}, {"range": "3580", "text": "3420"}, {"range": "3581", "text": "3420"}, {"range": "3582", "text": "3572"}, {"range": "3583", "text": "3420"}, {"range": "3584", "text": "3420"}, "Replace `········` with `··········`", {"range": "3585", "text": "3586"}, {"range": "3587", "text": "3420"}, {"range": "3588", "text": "3420"}, {"range": "3589", "text": "3572"}, "Replace `},·[readOnly,·state.data]` with `··},␍⏎······[readOnly,·state.data]␍⏎····`", {"range": "3590", "text": "3591"}, {"range": "3592", "text": "3416"}, {"range": "3593", "text": "3568"}, {"range": "3594", "text": "3420"}, {"range": "3595", "text": "3420"}, {"range": "3596", "text": "3420"}, {"range": "3597", "text": "3420"}, {"range": "3598", "text": "3420"}, {"range": "3599", "text": "3591"}, {"range": "3600", "text": "3568"}, {"range": "3601", "text": "3420"}, {"range": "3602", "text": "3420"}, {"range": "3603", "text": "3420"}, {"range": "3604", "text": "3420"}, {"range": "3605", "text": "3420"}, {"range": "3606", "text": "3586"}, {"range": "3607", "text": "3420"}, {"range": "3608", "text": "3420"}, {"range": "3609", "text": "3572"}, "Replace `······dispatch({·type:·'SET_DATA',·payload:·{·...state.data,·headers:·newHeaders,·rows:·newRows·}` with `········dispatch({␍⏎··········type:·'SET_DATA',␍⏎··········payload:·{·...state.data,·headers:·newHeaders,·rows:·newRows·},␍⏎·······`", {"range": "3610", "text": "3611"}, {"range": "3612", "text": "3591"}, "Replace `(␍⏎····<SpreadsheetContext.Provider·value={contextValue}>␍⏎······{children}␍⏎····</SpreadsheetContext.Provider>␍⏎··)` with `<SpreadsheetContext.Provider·value={contextValue}>{children}</SpreadsheetContext.Provider>`", {"range": "3613", "text": "3614"}, "React Hook useCallback has a missing dependency: 'workbookId'. Either include it or remove the dependency array. If 'setSpreadsheetData' needs the current value of 'workbookId', you can also switch to useReducer instead of useState and read 'workbookId' in the reducer.", ["3615"], "React Hook useCallback has an unnecessary dependency: 'desktopBridge'. Either exclude it or remove the dependency array. Outer scope values like 'desktopBridge' aren't valid dependencies because mutating them doesn't re-render the component.", ["3616"], "React Hook useEffect has a missing dependency: 'showIndicator'. Either include it or remove the dependency array.", ["3617"], ["3618"], "React Hook useCallback has a missing dependency: 'saveSpreadsheet'. Either include it or remove the dependency array.", ["3619"], ["3620"], "React Hook useCallback has a missing dependency: 'showFeedback'. Either include it or remove the dependency array.", ["3621"], ["3622"], "`react` import should occur after import of `lucide-react`", {"range": "3623", "text": "3624"}, "`./SpreadsheetContext` import should occur after import of `./components/SpreadsheetModals`", {"range": "3625", "text": "3626"}, "`./hooks/useSpreadsheetData` import should occur after import of `./components/SpreadsheetModals`", {"range": "3627", "text": "3628"}, "`./hooks/useSpreadsheetUI` import should occur after import of `./components/SpreadsheetModals`", {"range": "3629", "text": "3630"}, "`./hooks/useSpreadsheetKeyboard` import should occur after import of `./components/SpreadsheetModals`", {"range": "3631", "text": "3632"}, "`./components/SpreadsheetToolbar` import should occur after import of `./components/SpreadsheetModals`", {"range": "3633", "text": "3634"}, "`./components/SpreadsheetGrid` import should occur after import of `./components/MobileChat`", {"range": "3635", "text": "3636"}, {"range": "3637", "text": "3510"}, {"range": "3638", "text": "3420"}, {"range": "3639", "text": "3420"}, {"range": "3640", "text": "3558"}, {"range": "3641", "text": "3420"}, {"range": "3642", "text": "3420"}, {"range": "3643", "text": "3420"}, {"range": "3644", "text": "3420"}, "Replace `␍⏎······initialData={initialData}␍⏎······workbookId={workbookId}␍⏎······readOnly={readOnly}␍⏎····` with `·initialData={initialData}·workbookId={workbookId}·readOnly={readOnly}`", {"range": "3645", "text": "3646"}, ["3647"], ["3648"], "React Hook useCallback has a missing dependency: 'options'. Either include it or remove the dependency array. Outer scope values like 'feedbackService' aren't valid dependencies because mutating them doesn't re-render the component.", ["3649"], ["3650"], {"range": "3651", "text": "3297"}, "React Hook useCallback has a missing dependency: 'listFiles'. Either include it or remove the dependency array.", ["3652"], ["3653"], {"range": "3654", "text": "3297"}, "no-var", "Unexpected var, use let or const instead.", "VariableDeclaration", "<PERSON><PERSON><PERSON>", ["3655"], ["3656", "3657"], ["3658"], ["3659", "3660"], ["3661"], ["3662", "3663"], ["3664"], ["3665", "3666"], ["3667"], ["3668", "3669"], "Delete `····`", {"range": "3670", "text": "3342"}, "Replace `␍⏎······.createHash('sha256')␍⏎······.update(hashInput)␍⏎······.digest('hex')␍⏎······` with `.createHash('sha256').update(hashInput).digest('hex')`", {"range": "3671", "text": "3672"}, {"range": "3673", "text": "3342"}, {"range": "3674", "text": "3342"}, {"range": "3675", "text": "3342"}, {"range": "3676", "text": "3342"}, {"range": "3677", "text": "3342"}, ["3678", "3679"], {"range": "3680", "text": "3342"}, "Insert `␍⏎·········`", {"range": "3681", "text": "3682"}, "Insert `····`", {"range": "3683", "text": "3684"}, "Replace `··········` with `··············`", {"range": "3685", "text": "3686"}, {"range": "3687", "text": "3684"}, "Replace `}` with `····}␍⏎·········`", {"range": "3688", "text": "3689"}, {"range": "3690", "text": "3342"}, "Replace `␍⏎········cacheKey,␍⏎········cachedResult,␍⏎········CACHE_TTL.AI_COMMAND_RESULTS␍⏎······` with `cacheKey,·cachedResult,·CACHE_TTL.AI_COMMAND_RESULTS`", {"range": "3691", "text": "3692"}, {"range": "3693", "text": "3342"}, {"range": "3694", "text": "3342"}, {"range": "3695", "text": "3342"}, {"range": "3696", "text": "3342"}, {"range": "3697", "text": "3342"}, {"range": "3698", "text": "3342"}, {"range": "3699", "text": "3342"}, {"range": "3700", "text": "3342"}, {"range": "3701", "text": "3342"}, {"range": "3702", "text": "3342"}, {"range": "3703", "text": "3342"}, {"range": "3704", "text": "3342"}, {"range": "3705", "text": "3342"}, {"range": "3706", "text": "3342"}, {"range": "3707", "text": "3342"}, {"range": "3708", "text": "3342"}, {"range": "3709", "text": "3342"}, {"range": "3710", "text": "3342"}, {"range": "3711", "text": "3342"}, {"range": "3712", "text": "3342"}, "Replace `␍⏎······patterns.map(pattern·=>·this.invalidatePattern(pattern))␍⏎····` with `patterns.map(pattern·=>·this.invalidatePattern(pattern))`", {"range": "3713", "text": "3714"}, "Replace `␍⏎······`workbook:meta:${workbookId}`,␍⏎······`permissions:*:${workbookId}`,␍⏎····` with ``workbook:meta:${workbookId}`,·`permissions:*:${workbookId}``", {"range": "3715", "text": "3716"}, {"range": "3717", "text": "3714"}, {"range": "3718", "text": "3342"}, "Replace `␍⏎········CACHE_KEYS.TEMPLATES(),␍⏎········popularTemplates,␍⏎········CACHE_TTL.TEMPLATES␍⏎······` with `CACHE_KEYS.TEMPLATES(),·popularTemplates,·CACHE_TTL.TEMPLATES`", {"range": "3719", "text": "3720"}, {"range": "3721", "text": "3342"}, "Replace `␍⏎········CACHE_KEYS.POPULAR_AI_COMMANDS(),␍⏎········popularCommands,␍⏎········CACHE_TTL.POPULAR_COMMANDS␍⏎······` with `CACHE_KEYS.POPULAR_AI_COMMANDS(),·popularCommands,·CACHE_TTL.POPULAR_COMMANDS`", {"range": "3722", "text": "3723"}, {"range": "3724", "text": "3342"}, "Replace `␍⏎······this.warmupPopularTemplates(),␍⏎······this.warmupPopularAICommands(),␍⏎····` with `this.warmupPopularTemplates(),·this.warmupPopularAICommands()`", {"range": "3725", "text": "3726"}, ["3727", "3728"], "Replace `(ttlSeconds·*·1000)·:·undefined` with `ttlSeconds·*·1000·:·undefined,`", {"range": "3729", "text": "3730"}, ["3731"], ["3732"], ["3733"], ["3734"], ["3735"], ["3736"], ["3737"], ["3738"], ["3739"], ["3740"], ["3741"], ["3742"], ["3743"], ["3744"], ["3745"], ["3746"], ["3747"], ["3748"], ["3749"], ["3750"], ["3751"], ["3752"], ["3753"], ["3754"], ["3755"], ["3756"], ["3757"], ["3758"], ["3759"], ["3760"], ["3761"], ["3762"], ["3763"], ["3764"], ["3765"], ["3766"], ["3767"], ["3768"], ["3769"], ["3770"], ["3771"], ["3772"], ["3773"], ["3774"], ["3775"], ["3776"], ["3777"], ["3778"], ["3779"], ["3780"], ["3781"], ["3782"], ["3783"], ["3784"], ["3785"], ["3786"], ["3787"], ["3788"], ["3789"], ["3790"], ["3791"], ["3792"], ["3793"], ["3794"], ["3795"], ["3796"], ["3797"], ["3798"], ["3799"], ["3800"], ["3801"], ["3802"], {"range": "3803", "text": "3297"}, {"range": "3804", "text": "3297"}, ["3805"], ["3806"], ["3807"], ["3808"], ["3809"], ["3810"], ["3811"], ["3812"], ["3813"], ["3814"], ["3815"], ["3816"], ["3817"], ["3818"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["3819"], "Replace `·globalThis.__prisma·||` with `␍⏎··globalThis.__prisma·||␍⏎·`", {"range": "3820", "text": "3821"}, {"range": "3822", "text": "3420"}, {"range": "3823", "text": "3420"}, ["3824"], ["3825"], ["3826"], ["3827"], ["3828"], ["3829"], {"range": "3830", "text": "3297"}, {"range": "3831", "text": "3297"}, "Replace `␍⏎··············userId,␍⏎··············workbook.id,␍⏎··············data.aiCommand␍⏎············` with `userId,·workbook.id,·data.aiCommand`", {"range": "3832", "text": "3833"}, "Delete `␍⏎`", {"range": "3834", "text": "3342"}, "Replace `·'workbook_created'·|·'workbook_updated'·|·'collaboration_started'·|·'collaboration_ended'·|·'ai_command_executed'` with `␍⏎····|·'workbook_created'␍⏎····|·'workbook_updated'␍⏎····|·'collaboration_started'␍⏎····|·'collaboration_ended'␍⏎····|·'ai_command_executed'␍⏎···`", {"range": "3835", "text": "3836"}, {"range": "3837", "text": "3297"}, {"range": "3838", "text": "3297"}, {"range": "3839", "text": "3297"}, {"range": "3840", "text": "3297"}, "Replace `·typeof·DASHBOARD_REFRESH_INTERVALS` with `␍⏎··(typeof·DASHBOARD_REFRESH_INTERVALS)`", {"range": "3841", "text": "3842"}, "Replace `obj·&&·` with `(␍⏎····obj·&&`", {"range": "3843", "text": "3844"}, "Insert `␍⏎··)`", {"range": "3845", "text": "3846"}, "Insert `(␍⏎····`", {"range": "3847", "text": "3848"}, {"range": "3849", "text": "3846"}, {"range": "3850", "text": "3848"}, {"range": "3851", "text": "3846"}, "`../lib/operations` import should occur before import of `../types`", {"range": "3852", "text": "3853"}, "`../lib/logger` import should occur before import of `../types`", {"range": "3854", "text": "3855"}, ["3856", "3857"], ["3858", "3859"], {"range": "3860", "text": "3297"}, "Replace `·updatedData:·unknown;·resultSummary:·string;·modifiedCells?:·Array<{·row:·number;·col:·number·}>·` with `␍⏎··updatedData:·unknown;␍⏎··resultSummary:·string;␍⏎··modifiedCells?:·Array<{·row:·number;·col:·number·}>;␍⏎`", {"range": "3861", "text": "3862"}, {"range": "3863", "text": "3342"}, "Replace `(error)` with `error`", {"range": "3864", "text": "3865"}, {"range": "3866", "text": "3510"}, {"range": "3867", "text": "3420"}, {"range": "3868", "text": "3420"}, {"range": "3869", "text": "3420"}, {"range": "3870", "text": "3420"}, "Replace `(event)` with `event`", {"range": "3871", "text": "3872"}, "Replace `␍⏎······'FORMULA',␍⏎······'CHART',␍⏎······'FILTER',␍⏎······'SORT',␍⏎······'CELL_UPDATE',␍⏎······'TABLE',␍⏎····` with `'FORMULA',·'CHART',·'FILTER',·'SORT',·'CELL_UPDATE',·'TABLE'`", {"range": "3873", "text": "3874"}, "Insert `␍⏎`", {"range": "3875", "text": "3876"}, [56, 56], "\n", {"messageId": "3877", "fix": "3878", "desc": "3879"}, {"messageId": "3880", "fix": "3881", "desc": "3882"}, {"messageId": "3877", "fix": "3883", "desc": "3879"}, {"messageId": "3880", "fix": "3884", "desc": "3882"}, {"messageId": "3877", "fix": "3885", "desc": "3879"}, {"messageId": "3880", "fix": "3886", "desc": "3882"}, {"messageId": "3877", "fix": "3887", "desc": "3879"}, {"messageId": "3880", "fix": "3888", "desc": "3882"}, {"messageId": "3877", "fix": "3889", "desc": "3879"}, {"messageId": "3880", "fix": "3890", "desc": "3882"}, [588, 588], ",", {"messageId": "3891", "data": "3892", "fix": "3893", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "3896", "fix": "3897", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "3898", "desc": "3879"}, {"messageId": "3880", "fix": "3899", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "3900", "desc": "3879"}, {"messageId": "3880", "fix": "3901", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "3902", "fix": "3903", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, [729, 729], {"messageId": "3877", "fix": "3904", "desc": "3879"}, {"messageId": "3880", "fix": "3905", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "3906", "desc": "3879"}, {"messageId": "3880", "fix": "3907", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "3908", "desc": "3879"}, {"messageId": "3880", "fix": "3909", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "3910", "desc": "3879"}, {"messageId": "3880", "fix": "3911", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "3912", "desc": "3879"}, {"messageId": "3880", "fix": "3913", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "3914", "desc": "3879"}, {"messageId": "3880", "fix": "3915", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, [4003, 4003], {"kind": "3895", "justification": "3342"}, {"desc": "3916", "fix": "3917"}, {"kind": "3895", "justification": "3342"}, [1845, 1845], {"messageId": "3891", "data": "3918", "fix": "3919", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"desc": "3920", "fix": "3921"}, {"kind": "3895", "justification": "3342"}, {"desc": "3922", "fix": "3923"}, {"kind": "3895", "justification": "3342"}, [139, 139], [1024, 1025], "", [1039, 1040], [1102, 1102], [2022, 2025], "e", [3642, 3642], [3863, 3863], [4120, 4120], [4401, 4401], [4408, 4408], [4710, 4710], [4932, 4932], [4939, 4939], [4985, 4987], "''", [5114, 5173], "Comece rapidamente com suas planilhas", [5214, 5220], [5394, 5402], "action", [5560, 5617], "'h-auto p-4 flex flex-col items-start space-y-2 relative'", [5655, 5686], "'opacity-50 cursor-not-allowed'", [6362, 6416], "{action.description}", [6593, 6601], [7382, 7382], [7525, 7525], [7674, 7674], [7681, 7681], [7894, 7896], [8031, 8079], "Comece com modelos prontos", [8120, 8126], [8211, 8221], "template", [8485, 8559], "'mr-3', template.color)}>{template.icon}", [8685, 8693], [8710, 8711], [8740, 8741], [8762, 8763], [9064, 9065], [9103, 9104], [9110, 9111], [9478, 9480], [9609, 9659], "<PERSON><PERSON> plan<PERSON>has mais recentes", [9700, 9706], [9809, 9819], "workbook", [10231, 10284], "{workbook.name}", [10537, 10545], [10606, 10607], [10638, 10639], [10662, 10663], [17, 244], "import { formatDistanceToNow } from 'date-fns';\r\nimport { ptBR } from 'date-fns/locale';\r\nimport { \r\n  FileSpreadsheet, \r\n  Edit3, \r\n  Users, \r\n  Sparkles, \r\n  Clock,\r\n  ExternalLink,\r\n  MoreHorizontal\r\n} from 'lucide-react';\r\n", [25, 26], [46, 47], [57, 58], [68, 69], [82, 83], [128, 128], [1308, 1308], [1432, 1432], [1554, 1554], [1685, 1685], [1690, 1690], [1900, 1902], [2047, 2047], [2362, 2362], "\r\n       ", [2379, 2456], "          'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center'", [2459, 2459], "  ", [2489, 2491], "  )}\r\n      ", [2522, 2531], "'h-4 w-4'", [2826, 2872], "{activity.title}", [3185, 3195], [3320, 3321], [3354, 3355], [3382, 3383], [4008, 4054], "<PERSON><PERSON>", [4223, 4267], "{activity.description}", [4600, 4612], [4657, 4734], "<span>por {activity.metadata.sharedBy}</span>", [4737, 4749], [4796, 4875], "<span>com {activity.metadata.sharedWith}</span>", [5559, 5560], [5575, 5576], [5590, 5591], [5605, 5606], [5623, 5624], [5751, 5753], [5970, 6035], "Suas últimas ações no Excel Copilot", [6304, 6310], [6675, 6685], "activity", [7039, 7094], "Nenhuma atividade recente", [7435, 7436], [7451, 7452], [7466, 7467], [7481, 7482], [7498, 7499], [7649, 7660], "'space-y-2'", [8121, 8132], [8177, 8187], [8411, 8411], [8545, 8545], "\r\n             ", [8576, 8645], "  'flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center'", [8648, 8648], [8690, 8692], "  )}\r\n            ", [8729, 8738], "'h-3 w-3'", {"desc": "3924", "fix": "3925"}, {"desc": "3926", "fix": "3927"}, [17, 112], "import { <PERSON><PERSON><PERSON>, ArrowRight, AlertCircle } from 'lucide-react';\r\nimport React from 'react';\r\n", [114, 289], "import { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { <PERSON><PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\n", [114, 351], "import { ChatInterface } from '@/components/chat-interface';\r\nimport { <PERSON><PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\n", [3635, 3635], [3668, 3668], {"messageId": "3928", "data": "3929", "fix": "3930", "desc": "3931"}, {"messageId": "3928", "data": "3932", "fix": "3933", "desc": "3934"}, {"messageId": "3928", "data": "3935", "fix": "3936", "desc": "3937"}, {"messageId": "3928", "data": "3938", "fix": "3939", "desc": "3940"}, {"messageId": "3928", "data": "3941", "fix": "3942", "desc": "3931"}, {"messageId": "3928", "data": "3943", "fix": "3944", "desc": "3934"}, {"messageId": "3928", "data": "3945", "fix": "3946", "desc": "3937"}, {"messageId": "3928", "data": "3947", "fix": "3948", "desc": "3940"}, [17, 87], "import { Sparkles } from 'lucide-react';\r\nimport React from 'react';\r\n", [89, 288], "import { ChatInterface } from '@/components/chat-interface';\r\nimport { <PERSON><PERSON> } from '@/components/ui/button';\r\nimport { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';\r\n", [1434, 1574], "readOnly ? 'Modo somente leitura' : 'Digite um comando para a IA...'", [17, 125], "import { useVirtualizer } from '@tanstack/react-virtual';\r\nimport React, { useMemo, useRef } from 'react';\r\n", [1304, 1310], [2564, 2615], " lastModifiedCell.row === rowIndex &&", [3502, 3516], [17, 115], "import { Keyboard, Crown, Star, MessageSquare } from 'lucide-react';\r\nimport React from 'react';\r\n", [167, 394], "import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\n", [117, 442], "import { Badge } from '@/components/ui/badge';\r\nimport { <PERSON><PERSON> } from '@/components/ui/button';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\n", [1473, 1473], "\r\n   ", [3418, 3418], [6466, 6546], "Bem-vindo ao Excel Copilot! ({tutorialStep + 1}/4)", [6925, 6934], [6954, 6954], "natural. ", {"messageId": "3928", "data": "3949", "fix": "3950", "desc": "3931"}, {"messageId": "3928", "data": "3951", "fix": "3952", "desc": "3934"}, {"messageId": "3928", "data": "3953", "fix": "3954", "desc": "3937"}, {"messageId": "3928", "data": "3955", "fix": "3956", "desc": "3940"}, {"messageId": "3928", "data": "3957", "fix": "3958", "desc": "3931"}, {"messageId": "3928", "data": "3959", "fix": "3960", "desc": "3934"}, {"messageId": "3928", "data": "3961", "fix": "3962", "desc": "3937"}, {"messageId": "3928", "data": "3963", "fix": "3964", "desc": "3940"}, [7335, 7336], {"messageId": "3928", "data": "3965", "fix": "3966", "desc": "3931"}, {"messageId": "3928", "data": "3967", "fix": "3968", "desc": "3934"}, {"messageId": "3928", "data": "3969", "fix": "3970", "desc": "3937"}, {"messageId": "3928", "data": "3971", "fix": "3972", "desc": "3940"}, {"messageId": "3928", "data": "3973", "fix": "3974", "desc": "3931"}, {"messageId": "3928", "data": "3975", "fix": "3976", "desc": "3934"}, {"messageId": "3928", "data": "3977", "fix": "3978", "desc": "3937"}, {"messageId": "3928", "data": "3979", "fix": "3980", "desc": "3940"}, [7777, 7809], " e ? para ver\r\n                 ", [8203, 8203], " ou", [8222, 8225], [17, 220], "import {\r\n  ChevronRight,\r\n  FileSpreadsheet,\r\n  BarChart,\r\n  Undo,\r\n  Redo,\r\n  Save,\r\n  Loader2,\r\n  KeyboardIcon,\r\n  FullscreenIcon,\r\n  ChevronLeft,\r\n} from 'lucide-react';\r\nimport React from 'react';\r\n", [4620, 4670], "\r\n                    component: 'SpreadsheetToolbar',\r\n                    action: 'charts',\r\n                 ", [6385, 6542], " variant=\"ghost\" size=\"sm\" onClick={onToggleAIPanel} className=\"h-8 px-3\"", [599, 635], "\r\n  workbookId,\r\n  onSave,\r\n  initialCommand,\r\n", {"messageId": "3877", "fix": "3981", "desc": "3879"}, {"messageId": "3880", "fix": "3982", "desc": "3882"}, [4104, 4233], "op.type === 'cell_update' && typeof op.row === 'number' && typeof op.col === 'number'", {"messageId": "3877", "fix": "3983", "desc": "3879"}, {"messageId": "3880", "fix": "3984", "desc": "3882"}, [0, 105], "import { useRouter } from 'next/navigation';\r\nimport { useCallback, useEffect, useState } from 'react';\r\n", [3055, 3055], "\r\n    ", [3076, 3080], "      ", [3100, 3100], [3130, 3130], [3168, 3172], [3177, 3191], "    },\r\n    [actions]\r\n  ", [5829, 5846], "\r\n      apiUsageInfo &&", [5902, 5903], [6097, 6097], "\r\n      ", [6155, 6155], [6254, 6254], [6299, 6305], "        ", [6351, 6351], [6425, 6425], [6445, 6445], [6493, 6493], [6577, 6577], [6622, 6622], [6683, 6683], [6749, 6749], [6773, 6773], [6828, 6834], [6839, 6839], [6896, 6896], [6938, 6946], "          ", [6989, 6989], [7006, 7006], [7057, 7063], [7157, 7182], "  },\r\n      [readOnly, state.data]\r\n    ", [7347, 7347], [8597, 8597], [8628, 8628], [8653, 8653], [8727, 8727], [8766, 8766], [8810, 8810], [8890, 8915], [8951, 8951], [8976, 8976], [9007, 9007], [9081, 9081], [9126, 9126], [9173, 9173], [9219, 9227], [9253, 9253], [9298, 9298], [9314, 9320], [9327, 9424], "        dispatch({\r\n          type: 'SET_DATA',\r\n          payload: { ...state.data, headers: newHeaders, rows: newRows },\r\n       ", [9434, 9459], [9579, 9695], "<SpreadsheetContext.Provider value={contextValue}>{children}</SpreadsheetContext.Provider>", {"desc": "3985", "fix": "3986"}, {"desc": "3987", "fix": "3988"}, {"desc": "3989", "fix": "3990"}, {"kind": "3895", "justification": "3342"}, {"desc": "3991", "fix": "3992"}, {"kind": "3895", "justification": "3342"}, {"desc": "3993", "fix": "3994"}, {"kind": "3895", "justification": "3342"}, [17, 120], "import { MessageSquare } from 'lucide-react';\r\nimport React, { useEffect, useCallback } from 'react';\r\n", [250, 857], "import { useSpreadsheetData } from './hooks/useSpreadsheetData';\r\nimport { useSpreadsheetUI } from './hooks/useSpreadsheetUI';\r\nimport { useSpreadsheetKeyboard } from './hooks/useSpreadsheetKeyboard';\r\nimport { SpreadsheetToolbar } from './components/SpreadsheetToolbar';\r\nimport { SpreadsheetGrid } from './components/SpreadsheetGrid';\r\nimport { AIAssistantPanel } from './components/AIAssistantPanel';\r\nimport { MobileChat } from './components/MobileChat';\r\nimport { SpreadsheetModals } from './components/SpreadsheetModals';\r\nimport { SpreadsheetProvider, SpreadsheetData } from './SpreadsheetContext';\r\n", [328, 857], "import { useSpreadsheet<PERSON> } from './hooks/useSpreadsheetUI';\r\nimport { useSpreadsheetKeyboard } from './hooks/useSpreadsheetKeyboard';\r\nimport { SpreadsheetToolbar } from './components/SpreadsheetToolbar';\r\nimport { SpreadsheetGrid } from './components/SpreadsheetGrid';\r\nimport { AIAssistantPanel } from './components/AIAssistantPanel';\r\nimport { MobileChat } from './components/MobileChat';\r\nimport { SpreadsheetModals } from './components/SpreadsheetModals';\r\nimport { useSpreadsheetData } from './hooks/useSpreadsheetData';\r\n", [394, 857], "import { useSpreadsheetKeyboard } from './hooks/useSpreadsheetKeyboard';\r\nimport { SpreadsheetToolbar } from './components/SpreadsheetToolbar';\r\nimport { SpreadsheetGrid } from './components/SpreadsheetGrid';\r\nimport { AIAssistantPanel } from './components/AIAssistantPanel';\r\nimport { MobileChat } from './components/MobileChat';\r\nimport { SpreadsheetModals } from './components/SpreadsheetModals';\r\nimport { useSpreadsheetUI } from './hooks/useSpreadsheetUI';\r\n", [456, 857], "import { SpreadsheetToolbar } from './components/SpreadsheetToolbar';\r\nimport { SpreadsheetGrid } from './components/SpreadsheetGrid';\r\nimport { AIAssistantPanel } from './components/AIAssistantPanel';\r\nimport { MobileChat } from './components/MobileChat';\r\nimport { SpreadsheetModals } from './components/SpreadsheetModals';\r\nimport { useSpreadsheetKeyboard } from './hooks/useSpreadsheetKeyboard';\r\n", [530, 857], "import { SpreadsheetGrid } from './components/SpreadsheetGrid';\r\nimport { AIAssistantPanel } from './components/AIAssistantPanel';\r\nimport { MobileChat } from './components/MobileChat';\r\nimport { SpreadsheetModals } from './components/SpreadsheetModals';\r\nimport { SpreadsheetToolbar } from './components/SpreadsheetToolbar';\r\n", [601, 788], "import { AIAssistantPanel } from './components/AIAssistantPanel';\r\nimport { MobileChat } from './components/MobileChat';\r\nimport { SpreadsheetGrid } from './components/SpreadsheetGrid';\r\n", [2153, 2153], [2184, 2184], [2214, 2214], [2229, 2233], [2248, 2248], [2299, 2299], [2346, 2346], [2374, 2374], [7094, 7191], " initialData={initialData} workbookId={workbookId} readOnly={readOnly}", {"messageId": "3891", "data": "3995", "fix": "3996", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"desc": "3997", "fix": "3998"}, {"kind": "3895", "justification": "3342"}, [285, 285], {"desc": "3999", "fix": "4000"}, {"kind": "3895", "justification": "3342"}, [980, 980], {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "4001", "desc": "3879"}, {"messageId": "3880", "fix": "4002", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "4003", "desc": "3879"}, {"messageId": "3880", "fix": "4004", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "4005", "desc": "3879"}, {"messageId": "3880", "fix": "4006", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "4007", "desc": "3879"}, {"messageId": "3880", "fix": "4008", "desc": "3882"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3877", "fix": "4009", "desc": "3879"}, {"messageId": "3880", "fix": "4010", "desc": "3882"}, [996, 1000], [1019, 1104], ".createHash('sha256').update(hashInput).digest('hex')", [1604, 1610], [1691, 1697], [1879, 1887], [1922, 1928], [2081, 2087], {"messageId": "3877", "fix": "4011", "desc": "3879"}, {"messageId": "3880", "fix": "4012", "desc": "3882"}, [2748, 2754], [2939, 2939], "\r\n         ", [2945, 2945], "    ", [2988, 2998], "              ", [3032, 3032], [3084, 3085], "    }\r\n         ", [3110, 3116], [3163, 3251], "cacheKey, cached<PERSON><PERSON><PERSON>, CACHE_TTL.AI_COMMAND_RESULTS", [3255, 3261], [3510, 3516], [4024, 4030], [4148, 4154], [4538, 4544], [4658, 4664], [5015, 5021], [5203, 5209], [5660, 5666], [5811, 5817], [6310, 6316], [7139, 7145], [1714, 1720], [2324, 2330], [2441, 2447], [2784, 2790], [2856, 2862], [3816, 3822], [3898, 3904], [4329, 4335], [4970, 5040], "patterns.map(pattern => this.invalidatePattern(pattern))", [5267, 5349], "`workbook:meta:${workbookId}`, `permissions:*:${workbookId}`", [5377, 5447], [5736, 5742], [6086, 6183], "CACHE_KEYS.TEMPLATES(), popularTemplates, CACHE_TTL.TEMPLATES", [6567, 6573], [6892, 7005], "CACHE_KEYS.POPULAR_AI_COMMANDS(), popularCommands, CACHE_TTL.POPULAR_COMMANDS", [7362, 7366], [7391, 7474], "this.warmupPopularTemplates(), this.warmupPopularAICommands()", {"messageId": "3877", "fix": "4013", "desc": "3879"}, {"messageId": "3880", "fix": "4014", "desc": "3882"}, [1810, 1841], "ttlSeconds * 1000 : undefined,", {"messageId": "3891", "data": "4015", "fix": "4016", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4017", "fix": "4018", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4019", "fix": "4020", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4021", "fix": "4022", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4023", "fix": "4024", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4025", "fix": "4026", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4027", "fix": "4028", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4029", "fix": "4030", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4031", "fix": "4032", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4033", "fix": "4034", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4035", "fix": "4036", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4037", "fix": "4038", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4039", "fix": "4040", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4041", "fix": "4042", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4043", "fix": "4044", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4045", "fix": "4046", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4047", "fix": "4048", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4049", "fix": "4050", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4051", "fix": "4052", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4053", "fix": "4054", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4055", "fix": "4056", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4057", "fix": "4058", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4059", "fix": "4060", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4061", "fix": "4062", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4063", "fix": "4064", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4065", "fix": "4066", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4067", "fix": "4068", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4069", "fix": "4070", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4071", "fix": "4072", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4073", "fix": "4074", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4075", "fix": "4076", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4077", "fix": "4078", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4079", "fix": "4080", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4081", "fix": "4082", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4083", "fix": "4084", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4085", "fix": "4086", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, [700, 700], [836, 836], {"messageId": "3891", "data": "4087", "fix": "4088", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4089", "fix": "4090", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4091", "fix": "4092", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4093", "fix": "4094", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4095", "fix": "4096", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4097", "fix": "4098", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4099", "fix": "4100", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"kind": "3895", "justification": "3342"}, [231, 254], "\r\n  globalThis.__prisma ||\r\n ", [275, 275], [364, 364], {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4101", "fix": "4102", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"messageId": "3891", "data": "4103", "fix": "4104", "desc": "3894"}, {"kind": "3895", "justification": "3342"}, {"kind": "3895", "justification": "3342"}, [490, 490], [622, 622], [11315, 11410], "userId, workbook.id, data.aiCommand", [11584, 11586], [1712, 1826], "\r\n    | 'workbook_created'\r\n    | 'workbook_updated'\r\n    | 'collaboration_started'\r\n    | 'collaboration_ended'\r\n    | 'ai_command_executed'\r\n   ", [4805, 4805], [4991, 4991], [5192, 5192], [5405, 5405], [5632, 5667], "\r\n  (typeof DASHBOARD_REFRESH_INTERVALS)", [5820, 5827], "(\r\n    obj &&", [6002, 6002], "\r\n  )", [6084, 6084], "(\r\n    ", [6279, 6279], [6373, 6373], [6543, 6543], [287, 406], "import { executeOperation } from '../lib/operations';\r\nimport { ExcelOperation, ExcelOperationType } from '../types';\r\n", [287, 447], "import { logger } from '../lib/logger';\r\nimport { ExcelOperation, ExcelOperationType } from '../types';\r\nimport { executeOperation } from '../lib/operations';\r\n", {"messageId": "3877", "fix": "4105", "desc": "3879"}, {"messageId": "3880", "fix": "4106", "desc": "3882"}, {"messageId": "3877", "fix": "4107", "desc": "3879"}, {"messageId": "3880", "fix": "4108", "desc": "3882"}, [855, 855], [1548, 1646], "\r\n  updatedData: unknown;\r\n  resultSummary: string;\r\n  modifiedCells?: Array<{ row: number; col: number }>;\r\n", [4576, 4578], [5216, 5223], "error", [5363, 5363], [5391, 5391], [5408, 5408], [5445, 5445], [5462, 5462], [5758, 5765], "event", [6285, 6395], "'FORMULA', 'CHART', 'FILTER', 'SORT', 'CELL_UPDATE', 'TABLE'", [6407, 6407], "\r\n", "suggestUnknown", {"range": "4109", "text": "4110"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "4111", "text": "4112"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "4113", "text": "4110"}, {"range": "4114", "text": "4112"}, {"range": "4115", "text": "4110"}, {"range": "4116", "text": "4112"}, {"range": "4117", "text": "4110"}, {"range": "4118", "text": "4112"}, {"range": "4119", "text": "4110"}, {"range": "4120", "text": "4112"}, "removeConsole", {"propertyName": "4121"}, {"range": "4122", "text": "3342"}, "Remove the console.log().", "directive", {"propertyName": "4121"}, {"range": "4123", "text": "3342"}, {"range": "4124", "text": "4110"}, {"range": "4125", "text": "4112"}, {"range": "4126", "text": "4110"}, {"range": "4127", "text": "4112"}, {"propertyName": "4121"}, {"range": "4128", "text": "3342"}, {"range": "4129", "text": "4110"}, {"range": "4130", "text": "4112"}, {"range": "4131", "text": "4110"}, {"range": "4132", "text": "4112"}, {"range": "4133", "text": "4110"}, {"range": "4134", "text": "4112"}, {"range": "4135", "text": "4110"}, {"range": "4136", "text": "4112"}, {"range": "4137", "text": "4110"}, {"range": "4138", "text": "4112"}, {"range": "4139", "text": "4110"}, {"range": "4140", "text": "4112"}, "Update the dependencies array to be: [status, action, plan, isLoading, handleTrialSelect, handlePlanSelect]", {"range": "4141", "text": "4142"}, {"propertyName": "4121"}, {"range": "4143", "text": "3342"}, "Update the dependencies array to be: [initPhase, isLoading]", {"range": "4144", "text": "4145"}, "Update the dependencies array to be: [commandCategories, onSelect]", {"range": "4146", "text": "4147"}, "Update the dependencies array to be: [session?.user, searchQuery, fetchWithCSRF, workbooks.length, loadError]", {"range": "4148", "text": "4149"}, "Update the dependencies array to be: [fetchWorkbooks, session?.user?.id]", {"range": "4150", "text": "4151"}, "replaceWithAlt", {"alt": "4152"}, {"range": "4153", "text": "4154"}, "Replace with `&quot;`.", {"alt": "4155"}, {"range": "4156", "text": "4157"}, "Replace with `&ldquo;`.", {"alt": "4158"}, {"range": "4159", "text": "4160"}, "Replace with `&#34;`.", {"alt": "4161"}, {"range": "4162", "text": "4163"}, "Replace with `&rdquo;`.", {"alt": "4152"}, {"range": "4164", "text": "4165"}, {"alt": "4155"}, {"range": "4166", "text": "4167"}, {"alt": "4158"}, {"range": "4168", "text": "4169"}, {"alt": "4161"}, {"range": "4170", "text": "4171"}, {"alt": "4152"}, {"range": "4172", "text": "4173"}, {"alt": "4155"}, {"range": "4174", "text": "4175"}, {"alt": "4158"}, {"range": "4176", "text": "4177"}, {"alt": "4161"}, {"range": "4178", "text": "4179"}, {"alt": "4152"}, {"range": "4180", "text": "4181"}, {"alt": "4155"}, {"range": "4182", "text": "4183"}, {"alt": "4158"}, {"range": "4184", "text": "4185"}, {"alt": "4161"}, {"range": "4186", "text": "4187"}, {"alt": "4152"}, {"range": "4188", "text": "4189"}, {"alt": "4155"}, {"range": "4190", "text": "4191"}, {"alt": "4158"}, {"range": "4192", "text": "4193"}, {"alt": "4161"}, {"range": "4194", "text": "4195"}, {"alt": "4152"}, {"range": "4196", "text": "4197"}, {"alt": "4155"}, {"range": "4198", "text": "4199"}, {"alt": "4158"}, {"range": "4200", "text": "4201"}, {"alt": "4161"}, {"range": "4202", "text": "4203"}, {"range": "4204", "text": "4110"}, {"range": "4205", "text": "4112"}, {"range": "4206", "text": "4110"}, {"range": "4207", "text": "4112"}, "Update the dependencies array to be: [addToHistory, spreadsheetData, workbookId]", {"range": "4208", "text": "4209"}, "Update the dependencies array to be: []", {"range": "4210", "text": "4211"}, "Update the dependencies array to be: [createTimer, showIndicator]", {"range": "4212", "text": "4213"}, "Update the dependencies array to be: [saveSpreadsheet, handleUndo, handleRedo, handleAddRow, handleAddColumn, isFullScreen]", {"range": "4214", "text": "4215"}, "Update the dependencies array to be: [pendingCommand, showFeedback]", {"range": "4216", "text": "4217"}, {"propertyName": "4121"}, {"range": "4218", "text": "3342"}, "Update the dependencies array to be: [shouldUseMock, options, mockAIResponse, messages]", {"range": "4219", "text": "4220"}, "Update the dependencies array to be: [listFiles]", {"range": "4221", "text": "4222"}, {"range": "4223", "text": "4110"}, {"range": "4224", "text": "4112"}, {"range": "4225", "text": "4110"}, {"range": "4226", "text": "4112"}, {"range": "4227", "text": "4110"}, {"range": "4228", "text": "4112"}, {"range": "4229", "text": "4110"}, {"range": "4230", "text": "4112"}, {"range": "4231", "text": "4110"}, {"range": "4232", "text": "4112"}, {"range": "4233", "text": "4110"}, {"range": "4234", "text": "4112"}, {"range": "4235", "text": "4110"}, {"range": "4236", "text": "4112"}, {"propertyName": "4121"}, {"range": "4237", "text": "3342"}, {"propertyName": "4121"}, {"range": "4238", "text": "3342"}, {"propertyName": "4121"}, {"range": "4239", "text": "3342"}, {"propertyName": "4121"}, {"range": "4240", "text": "3342"}, {"propertyName": "4121"}, {"range": "4241", "text": "3342"}, {"propertyName": "4121"}, {"range": "4242", "text": "3342"}, {"propertyName": "4121"}, {"range": "4243", "text": "3342"}, {"propertyName": "4121"}, {"range": "4244", "text": "3342"}, {"propertyName": "4121"}, {"range": "4245", "text": "3342"}, {"propertyName": "4121"}, {"range": "4246", "text": "3342"}, {"propertyName": "4121"}, {"range": "4247", "text": "3342"}, {"propertyName": "4121"}, {"range": "4248", "text": "3342"}, {"propertyName": "4121"}, {"range": "4249", "text": "3342"}, {"propertyName": "4121"}, {"range": "4250", "text": "3342"}, {"propertyName": "4121"}, {"range": "4251", "text": "3342"}, {"propertyName": "4121"}, {"range": "4252", "text": "3342"}, {"propertyName": "4121"}, {"range": "4253", "text": "3342"}, {"propertyName": "4121"}, {"range": "4254", "text": "3342"}, {"propertyName": "4121"}, {"range": "4255", "text": "3342"}, {"propertyName": "4121"}, {"range": "4256", "text": "3342"}, {"propertyName": "4121"}, {"range": "4257", "text": "3342"}, {"propertyName": "4121"}, {"range": "4258", "text": "3342"}, {"propertyName": "4121"}, {"range": "4259", "text": "3342"}, {"propertyName": "4121"}, {"range": "4260", "text": "3342"}, {"propertyName": "4121"}, {"range": "4261", "text": "3342"}, {"propertyName": "4121"}, {"range": "4262", "text": "3342"}, {"propertyName": "4121"}, {"range": "4263", "text": "3342"}, {"propertyName": "4121"}, {"range": "4264", "text": "3342"}, {"propertyName": "4121"}, {"range": "4265", "text": "3342"}, {"propertyName": "4121"}, {"range": "4266", "text": "3342"}, {"propertyName": "4121"}, {"range": "4267", "text": "3342"}, {"propertyName": "4121"}, {"range": "4268", "text": "3342"}, {"propertyName": "4121"}, {"range": "4269", "text": "3342"}, {"propertyName": "4121"}, {"range": "4270", "text": "3342"}, {"propertyName": "4121"}, {"range": "4271", "text": "3342"}, {"propertyName": "4121"}, {"range": "4272", "text": "3342"}, {"propertyName": "4121"}, {"range": "4273", "text": "3342"}, {"propertyName": "4121"}, {"range": "4274", "text": "3342"}, {"propertyName": "4121"}, {"range": "4275", "text": "3342"}, {"propertyName": "4121"}, {"range": "4276", "text": "3342"}, {"propertyName": "4121"}, {"range": "4277", "text": "3342"}, {"propertyName": "4121"}, {"range": "4278", "text": "3342"}, {"propertyName": "4121"}, {"range": "4279", "text": "3342"}, {"propertyName": "4121"}, {"range": "4280", "text": "3342"}, {"propertyName": "4121"}, {"range": "4281", "text": "3342"}, {"range": "4282", "text": "4110"}, {"range": "4283", "text": "4112"}, {"range": "4284", "text": "4110"}, {"range": "4285", "text": "4112"}, [230, 233], "unknown", [230, 233], "never", [306, 309], [306, 309], [322, 325], [322, 325], [408, 411], [408, 411], [507, 510], [507, 510], "log", [1905, 2157], [5260, 5448], [765, 768], [765, 768], [999, 1002], [999, 1002], [6068, 6220], [580, 583], [580, 583], [589, 592], [589, 592], [2147, 2150], [2147, 2150], [2156, 2159], [2156, 2159], [5133, 5136], [5133, 5136], [5142, 5145], [5142, 5145], [4834, 4867], "[status, action, plan, isLoading, handleTrialSelect, handlePlanSelect]", [5444, 5576], [7310, 7312], "[initPhase, isLoading]", [6926, 6936], "[commandCategories, onSelect]", [7233, 7280], "[session?.user, searchQuery, fetchWithCSRF, workbooks.length, loadError]", [7731, 7750], "[fetchWorkbooks, session?.user?.id]", "&quot;", [4548, 4572], "Adicionar coluna &quot;Total\"", "&ldquo;", [4548, 4572], "Adicionar coluna &ldquo;Total\"", "&#34;", [4548, 4572], "Adicionar coluna &#34;Total\"", "&rdquo;", [4548, 4572], "Adicionar coluna &rdquo;Total\"", [4548, 4572], "Ad<PERSON><PERSON><PERSON> coluna \"Total&quot;", [4548, 4572], "Adicionar coluna \"Total&ldquo;", [4548, 4572], "Adicionar coluna \"Total&#34;", [4548, 4572], "Adicionar coluna \"Total&rdquo;", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como &quot;Adicione uma coluna Total\" ou \r\n                  \"Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como &ldquo;Adicione uma coluna Total\" ou \r\n                  \"Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como &#34;Adicione uma coluna Total\" ou \r\n                  \"Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como &rdquo;Adicione uma coluna Total\" ou \r\n                  \"Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total&quot; ou \r\n                  \"Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total&ldquo; ou \r\n                  \"Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total&#34; ou \r\n                  \"Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total&rdquo; ou \r\n                  \"Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total\" ou \r\n                  &quot;Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total\" ou \r\n                  &ldquo;Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total\" ou \r\n                  &#34;Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total\" ou \r\n                  &rdquo;Calcule a média dos valores\". A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total\" ou \r\n                  \"Calcule a média dos valores&quot;. A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total\" ou \r\n                  \"Calcule a média dos valores&ldquo;. A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total\" ou \r\n                  \"Calcule a média dos valores&#34;. A IA entenderá e executará automaticamente!\r\n                ", [7239, 7448], "\r\n                  Use o painel à direita para dar comandos como \"Adicione uma coluna Total\" ou \r\n                  \"Calcule a média dos valores&rdquo;. A IA entenderá e executará automaticamente!\r\n                ", [3518, 3521], [3518, 3521], [4930, 4933], [4930, 4933], [12569, 12621], "[addToHistory, spreadsheetData, workbookId]", [25700, 25715], "[]", [26884, 26897], "[create<PERSON><PERSON><PERSON>, showIndicator]", [29235, 29304], "[saveSpreadsheet, handleUndo, handleRedo, handleAddRow, handleAddColumn, isFullScreen]", [36847, 36874], "[pendingCom<PERSON>, showFeedback]", [8682, 8765], [14122, 14415], "[shouldUseMock, options, mockAIResponse, messages]", [2538, 2540], "[listFiles]", [366, 369], [366, 369], [633, 636], [633, 636], [867, 870], [867, 870], [1141, 1144], [1141, 1144], [278, 281], [278, 281], [2443, 2446], [2443, 2446], [8171, 8174], [8171, 8174], [17857, 17925], [17929, 17984], [17988, 18054], [18098, 18208], [18212, 18256], [18260, 18337], [18341, 18409], [18413, 18485], [18978, 19107], [19223, 19280], [19565, 19614], [19622, 19679], [19687, 19744], [20041, 20123], [20131, 20203], [20211, 20248], [20256, 20337], [20507, 20565], [20573, 20652], [20810, 20949], [21285, 21353], [21357, 21395], [21399, 21465], [21603, 21684], [21799, 21842], [21850, 21929], [21937, 21996], [22276, 22368], [22482, 22534], [22542, 22611], [22619, 22688], [22883, 22933], [22937, 23046], [23050, 23145], [23149, 23215], [23219, 23367], [9570, 9608], [3129, 3168], [3455, 3493], [3581, 3622], [3722, 3763], [3894, 3937], [4629, 4681], [4513, 4608], [4658, 4899], [623, 626], [623, 626], [724, 727], [724, 727]]